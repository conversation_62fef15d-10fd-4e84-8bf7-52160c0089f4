import { sendNotificationBySatelliteId } from './push-notifications';
import { satelliteInterface } from './satellite-interface';

// Funzione per inviare lo stato di connessione a tutti i client WebSocket
function broadcastConnectionStatus(connected: boolean, satelliteId?: string) {
  const now = new Date().toISOString();
  console.log(`[${now}][connection-status.ts][broadcastConnectionStatus] Inizio broadcast stato connessione: connected=${connected}, satelliteId=${satelliteId || 'sconosciuto'}`);

  // Aggiorna lo stato di connessione nell'interfaccia satellitare
  satelliteInterface.emit('connection-status', connected, satelliteId);

  console.log(`[${now}][connection-status.ts][broadcastConnectionStatus] Evento 'connection-status' emesso con successo: connected=${connected}, satelliteId=${satelliteId || 'sconosciuto'}`);
}

// Funzione per aggiornare lo stato di connessione del satellitare
export function updateConnectionStatus(connected: boolean, satelliteId?: string) {
  const now = new Date().toISOString();
  console.log(`[${now}][connection-status.ts][updateConnectionStatus] Inizio aggiornamento stato connessione: connected=${connected}, satelliteId=${satelliteId || 'sconosciuto'}`);

  if (!satelliteId) {
    console.log(`[${now}][connection-status.ts][updateConnectionStatus] Nessun satelliteId specificato, uscita`);
    return;
  }

  // Invia lo stato di connessione a tutti i client WebSocket
  broadcastConnectionStatus(connected, satelliteId);

  // Invia notifiche push quando lo stato di connessione cambia
  if (satelliteId) {
    if (connected) {
      try {
        sendNotificationBySatelliteId(
          satelliteId,
          'Satellitare connesso',
          'Il dispositivo satellitare è ora online.'
        ).catch(() => {
          // Errore nell'invio della notifica
        });
      } catch (error) {
        // Eccezione durante l'invio della notifica
      }
    } else {
      try {
        sendNotificationBySatelliteId(
          satelliteId,
          'Satellitare disconnesso',
          'Il dispositivo satellitare è offline.'
        ).catch(() => {
          // Errore nell'invio della notifica
        });
      } catch (error) {
        // Eccezione durante l'invio della notifica
      }
    }
  }
}

// Esporta la funzione come variabile globale
(global as any).updateConnectionStatus = updateConnectionStatus;
