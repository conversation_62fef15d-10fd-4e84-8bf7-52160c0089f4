import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { User } from "@shared/schema";
import { SatelliteIcon } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface SatelliteSelectorProps {
  user: User;
  onSatelliteSelect: (satelliteId: string) => void;
  currentSatelliteId?: string;
}

export default function SatelliteSelector({
  user,
  onSatelliteSelect,
  currentSatelliteId,
}: SatelliteSelectorProps) {
  const [satellites, setSatellites] = useState<string[]>([]);
  const isMobile = useIsMobile();

  // Estrai i satelliti dall'ID satellitare dell'utente
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Esecuzione effect con user=${user?.id}, currentSatelliteId=${currentSatelliteId}`);

    if (user?.satelliteId) {
      // Dividi la stringa satelliteId usando il carattere ':'
      const satelliteIds = user.satelliteId.split(':');
      console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Satelliti disponibili: ${satelliteIds.join(', ')}`);
      setSatellites(satelliteIds);

      // Se non c'è un satellitare selezionato, seleziona il primo
      // Importante: verifichiamo che currentSatelliteId sia strettamente null o undefined
      if (currentSatelliteId === null || currentSatelliteId === undefined) {
        if (satelliteIds.length > 0) {
          console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Nessun satellitare selezionato, seleziono automaticamente il primo: ${satelliteIds[0]}`);
          onSatelliteSelect(satelliteIds[0]);
        } else {
          console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Nessun satellitare disponibile per l'utente`);
        }
      } else {
        console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Satellitare già selezionato: ${currentSatelliteId}`);

        // Verifica che il satellitare selezionato sia ancora disponibile per l'utente
        if (!satelliteIds.includes(currentSatelliteId)) {
          console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Satellitare selezionato non più disponibile, seleziono il primo: ${satelliteIds[0]}`);
          if (satelliteIds.length > 0) {
            onSatelliteSelect(satelliteIds[0]);
          }
        }
      }
    } else {
      console.log(`[${now}][SatelliteSelector.tsx][useEffect-extractSatellites] Utente senza satelliti assegnati`);
    }
  }, [user, currentSatelliteId, onSatelliteSelect]);

  // Se c'è solo un satellitare, non mostrare il selettore
  if (satellites.length <= 1) {
    return null;
  }

  // Funzione per gestire il cambio di satellitare
  const handleSatelliteChange = (newSatelliteId: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][SatelliteSelector.tsx][handleSatelliteChange] Tentativo cambio satellitare a: ${newSatelliteId}`);

    // Verifica che il nuovo ID sia diverso da quello corrente
    if (newSatelliteId !== currentSatelliteId) {
      console.log(`[${now}][SatelliteSelector.tsx][handleSatelliteChange] Cambio satellitare da ${currentSatelliteId} a ${newSatelliteId}`);
      onSatelliteSelect(newSatelliteId);
    } else {
      console.log(`[${now}][SatelliteSelector.tsx][handleSatelliteChange] Ignorato cambio satellitare: nuovo ID uguale a quello corrente (${currentSatelliteId})`);
    }
  };

  return (
    <div className={`flex items-center ${isMobile ? 'w-full' : ''}`}>
      <Select
        value={currentSatelliteId}
        onValueChange={handleSatelliteChange}
      >
        <SelectTrigger className={`${isMobile ? 'w-full' : 'w-[170px]'} bg-teal-700 text-white border-teal-600 focus:ring-teal-400`}>
          <SelectValue placeholder="Seleziona satellitare" />
        </SelectTrigger>
        <SelectContent>
          {satellites.map((satId) => (
            <SelectItem key={satId} value={satId}>
              {satId}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
