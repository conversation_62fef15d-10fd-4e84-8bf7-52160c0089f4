/* Stili per l'header principale dell'applicazione su dispositivi mobili */
@media (max-width: 768px) {
  /* Contenitore principale dell'header */
  .mobile-app-header-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center; /* Allinea verticalmente al centro */
    padding: 0.25rem 0; /* Aumentato leggermente il padding verticale */
    overflow: hidden; /* Previene qualsiasi scrollbar */
    gap: 0.5rem;
    min-height: 6.5rem !important; /* Aumentato per assicurare che tutto il contenuto sia visibile */
    height: auto !important; /* Permette al contenitore di espandersi in base al contenuto */
  }

  /* Colonna sinistra dell'header */
  .mobile-app-header-left-column {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.2rem; /* Ridotto lo spazio tra gli elementi */
    max-width: 50%;
    overflow: visible; /* Assicura che il contenuto non venga tagliato */
  }

  /* Colonna destra dell'header */
  .mobile-app-header-right-column {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem; /* Aumentato lo spazio tra i pulsanti e il selettore */
    max-width: 50%;
    width: 50%; /* Assicura che occupi esattamente il 50% dello spazio disponibile */
    overflow: visible; /* Assicura che il contenuto non venga tagliato */
  }

  /* Contenitore del logo e titolo */
  .mobile-app-header-logo {
    display: flex;
    align-items: center;
    margin-bottom: 0.125rem; /* Ridotto il margine inferiore */
    overflow: visible; /* Assicura che il contenuto non venga tagliato */
  }

  /* Aumenta la dimensione del logo */
  .mobile-app-header-logo img {
    width: 2.75rem; /* Leggermente ridotto per evitare problemi di spazio */
    height: 2.75rem; /* Leggermente ridotto per evitare problemi di spazio */
    margin-right: 0.375rem; /* Ridotto leggermente */
    flex-shrink: 0; /* Impedisce al logo di restringersi */
  }

  /* Aumenta la dimensione del titolo */
  .mobile-app-header-logo h1 {
    font-size: 2.75rem; /* Leggermente ridotto per evitare problemi di spazio */
    line-height: 2.75rem;
    font-weight: 700; /* Più bold per maggiore visibilità */
    letter-spacing: -0.025em; /* Leggera riduzione della spaziatura tra lettere */
    white-space: nowrap; /* Impedisce al testo di andare a capo */
  }

  /* Stile per il nome utente */
  .mobile-app-header-username {
    font-size: 0.95rem; /* Leggermente ridotto per evitare problemi di spazio */
    line-height: 1.2;
    white-space: nowrap; /* Impedisce al testo di andare a capo */
    overflow: hidden; /* Nasconde il testo che eccede */
    text-overflow: ellipsis; /* Mostra i puntini di sospensione per il testo troncato */
    max-width: 70%; /* Limita la larghezza per lasciare spazio alle icone */
    margin-right: 0.5rem; /* Spazio tra il nome utente e le icone */
  }

  /* Container per le icone e il nome utente */
  .mobile-app-header-icons-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0.15rem;
    width: 100%;
  }

  /* Indicatore di stato connessione */
  .mobile-app-header-connection {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.25rem;
  }

  /* Stile per le icone di connessione */
  .mobile-app-header-connection .connection-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
  }

  /* Stile per il selettore satellitare in modalità mobile */
  .mobile-app-header-satellite-selector {
    width: 100%;
    margin-bottom: 0.125rem; /* Ridotto il margine */
    min-width: 100%; /* Assicura che sia almeno largo quanto il suo contenitore */
    overflow: visible; /* Assicura che il contenuto non venga tagliato */
  }

  /* Riduce l'altezza del selettore */
  .mobile-app-header-satellite-selector [data-radix-select-trigger] {
    height: 1.45rem; /* Leggermente ridotto per evitare problemi di spazio */
    min-height: 1.45rem; /* Leggermente ridotto per evitare problemi di spazio */
    font-size: 0.75rem;
    padding-top: 0;
    padding-bottom: 0;
    width: 100%; /* Assicura che il trigger occupi tutta la larghezza disponibile */
    overflow: hidden; /* Nasconde il testo che eccede */
    text-overflow: ellipsis; /* Mostra i puntini di sospensione per il testo troncato */
  }

  /* Contenitore per i pulsanti di azione */
  .mobile-app-header-actions {
    display: flex;
    flex-direction: row; /* Cambiato da column a row per affiancare i pulsanti */
    gap: 0.25rem; /* Spazio tra i pulsanti affiancati */
    width: 100%;
    justify-content: space-between; /* Distribuisce i pulsanti uniformemente */
    min-width: 100%; /* Assicura che occupi tutta la larghezza disponibile */
    overflow: visible; /* Assicura che il contenuto non venga tagliato */
  }

  /* Assicura che i pulsanti abbiano la stessa larghezza */
  .mobile-app-header-button {
    height: 1.65rem; /* Leggermente ridotto per evitare problemi di spazio */
    min-height: 1.5rem;
    font-size: 0.8rem; /* Leggermente ridotto per evitare problemi di spazio */
    padding: 0 0.4rem; /* Ridotto leggermente il padding */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 48% !important; /* Esattamente il 48% per ciascun pulsante */
    line-height: 1;
    flex: 1 1 0 !important; /* Forza i pulsanti ad avere la stessa larghezza */
  }

  /* Riduce la dimensione delle icone nei pulsanti */
  .mobile-app-header-button svg {
    width: 0.875rem;
    height: 0.875rem;
  }

  /* Forza l'header a non avere scrollbar orizzontale o verticale e imposta un'altezza minima */
  header {
    overflow: hidden; /* Previene sia scrollbar orizzontale che verticale */
    max-width: 100vw;
    padding: 0; /* Rimosso completamente il padding */
    min-height: 7rem !important; /* Aumentato per assicurare che tutto il contenuto sia visibile */
  }

  /* Imposta il padding del contenitore dell'header */
  header > div {
    padding-top: 0.25rem; /* Aumentato leggermente */
    padding-bottom: 0.25rem; /* Aumentato leggermente */
    min-height: 6.75rem !important; /* Aumentato per assicurare che tutto il contenuto sia visibile */
    overflow: visible; /* Assicura che il contenuto non venga tagliato */
  }

  /* Assicura che il contenitore principale non abbia scrollbar orizzontale */
  .h-screen {
    overflow-x: hidden;
  }
}
