import net from 'node:net';
const PORT = 8090;

// Importa connection-status per assicurarsi che la funzione updateConnectionStatus sia definita
import './connection-status.js';

const server = net.createServer((socket) => {
    socket.connectedMessage = false;
    socket.lat=0;
    socket.long=0;

    socket.on('data', (data) => {
        // Dati ricevuti
        const message = data.toString().trim();

        if (message.startsWith('*') || message.startsWith('$')) {
            const parts = message.slice(1, -1).split(',');

            if (message.startsWith('*')) {
                // Handle heartbeat data (messages starting with *)
                if ((parts.length >= 13) && (parts[2] == 'V1')) {
                    const maker = parts[0];
                    const serialNumber = parts[1];
                    //const cmd = parts[2];
                    const time = parts[3];
                    const validity = parts[4];
                    const latitude = parts[5];
                    //const latitudeDirection = parts[6];
                    const longitude = parts[7];
                    //const longitudeDirection = parts[8];
                    const speed = parts[9];
                    const direction = parts[10];
                    const date = parts[11];
                    //const vehicleStatus = parts[12];
                    // Stato del veicolo
                    let mcc, mnc, lac, cellId, iccid;

                    if (parts.length > 13) {
                      mcc = parts[13];
                      mnc = parts[14];
                      lac = parts[15];
                      cellId = parts[16];
                    }

                    if (parts.length > 17){
                      iccid = parts[17];
                    }

                    //latitude  calculations
                    const latDeg = parseInt(latitude.substring(0, 2));
                    const latMin = parseFloat(latitude.substring(2)) / 60;
                    const decimalLatitude = latDeg + latMin;

                    //longitude calculations
                    const longDeg = parseInt(longitude.substring(0, 3));
                    const longMin = parseFloat(longitude.substring(3)) / 60;
                    const decimalLongitude = longDeg + longMin;

                    //data e ora
                    let timedata= new Date(2000+parseInt(date.slice(4).toString('hex')),
                                        parseInt(date.slice(2, 4).toString('hex'))-1,
                                        parseInt(date.slice(0, 2).toString('hex')),
                                        parseInt(time.slice(0, 2).toString('hex')),
                                        parseInt(time.slice(2, 4).toString('hex')),
                                        parseInt(time.slice(4).toString('hex')));
                    //timedata.setHours(timedata.getHours() + 2);

                    socket.deviceImei = serialNumber;
                    // Decodifica dati heartbeat
                    const speedKmh = Math.round(parseFloat(speed)); // arrotonda al numero intero più vicino
                    // Invia i dati GPS al sistema principale
                    if(socket.lat!==decimalLatitude && socket.long!==decimalLongitude){
                        global.addGPSPosition(
                            maker, // Usa il maker come identificatore del dispositivo
                            decimalLatitude,
                            decimalLongitude,
                            speedKmh,
                            validity === 'A' ? 1 : 0, // Fixed if 'A', not fixed if 'V'
                            parseInt(direction),
                            0, // Non abbiamo informazioni sulla batteria, impostiamo a 0%
                            timedata, // Passa il timestamp del satellitare come oggetto Date
                            0 // Stato del dispositivo (0=Online, 1=Batteria, 2=Vibrazione, 3=Inizio, 4=Fine)
                        );
                        const now = new Date().toISOString();
                        console.log(`[${now}][Gestionesatelliti.js] addGPSPosition chiamato con successo per deviceImei=${maker}, lat=${decimalLatitude}, long=${decimalLongitude}, status=0`);
                        socket.lat=decimalLatitude;
                        socket.long=decimalLongitude;
                        socket.batteryVoltage = 0;
                        socket.lastMessageTime = timedata; // Memorizza il timestamp dell'ultimo messaggio
                        socket.direction = parseInt(direction);
                    }
                } else {
                    // Invalid heartbeat data format
                }
            } else if (message.startsWith('$')) {
                let byteArray = data;
                const decodedData = {};

                // Decodifica dati normali
                decodedData['Terminal Serial Number'] = byteArray.slice(1, 6).toString('hex');
                socket.deviceImei = decodedData['Terminal Serial Number'];
                let timedata= new Date(2000+parseInt(byteArray.slice(11, 12).toString('hex')),
                                        parseInt(byteArray.slice(10, 11).toString('hex'))-1,
                                        parseInt(byteArray.slice(9, 10).toString('hex')),
                                        parseInt(byteArray.slice(6, 7).toString('hex')),
                                        parseInt(byteArray.slice(7, 8).toString('hex')),
                                        parseInt(byteArray.slice(8, 9).toString('hex')));
                //timedata.setHours(timedata.getHours() + 2);
                decodedData['Giorno'] = timedata.toLocaleString('it-IT', {
                                        timeZone: 'Europe/Rome',
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        second: '2-digit'
                                    });

                const statusByte = byteArray[21];
                //const isEastLongitude = (statusByte & 0x08) !== 0;  // bit3
                //const isNorthLatitude = (statusByte & 0x04) !== 0;  // bit2
                const isValidGPS = (statusByte & 0x02) !== 0;       // bit1 (A/V)
                decodedData['Satellites'] = isValidGPS ? 1 : 0;

                // Decodifica latitudine
                let latitudeValue = byteArray.slice(12, 16).toString('hex');
                latitudeValue = latitudeValue.slice(0, 4) + "." + latitudeValue.slice(4);
                const latDeg = parseInt(latitudeValue.substring(0, 2));
                const latMin = parseFloat(latitudeValue.substring(2)) / 60;
                const decimalLatitude = latDeg + latMin;
                decodedData['Latitude'] = decimalLatitude;

                // Decodifica longitudine
                let longitudeValue = byteArray.slice(17, 22).toString('hex');
                longitudeValue = longitudeValue.slice(0, 5) + "." + longitudeValue.slice(5,9);
                const longDeg = parseInt(longitudeValue.substring(0, 3));
                const longMin = parseFloat(longitudeValue.substring(3)) / 60;
                const decimalLongitude = longDeg + longMin;
                decodedData['Longitude'] = decimalLongitude;

                decodedData['GPS_Status'] = isValidGPS ? true : false;
                // Il valore della batteria è espresso in percentuale (0-100)
                const batteryValue = byteArray[16] || 0;
                decodedData['Battery'] = batteryValue;

                // Calcola Velocità e direzione
                let spe_cou = byteArray.slice(22, 25);
                const speedKnots = (spe_cou[0] * 256 + spe_cou[1]) / 10;
                decodedData['Speed'] = Math.round(speedKnots);
                decodedData['Direction'] = spe_cou[2];

                decodedData['Vehicle Status'] = byteArray.slice(25, 29).reduce((acc, val) => acc + val.toString(16).padStart(2, '0'), ''); // Bytes 24-27
                // Dati decodificati
                // Invia i dati GPS al sistema principale
                if(socket.lat!==decimalLatitude && socket.long!==decimalLongitude){
                    global.addGPSPosition(
                        decodedData['Terminal Serial Number'],
                        decodedData['Latitude'],
                        decodedData['Longitude'], // Passa la longitudine come numero decimale
                        decodedData['Speed'], // Passa la velocità come numero intero
                        decodedData['Satellites'], // Passa il numero di satelliti come numero intero
                        decodedData['Direction'], // Passa la direzione come numero intero
                        decodedData['Battery'], // Passa il valore inpercentuale della batteria
                        timedata, // Passa il timestamp del satellitare come oggetto Date
                        0 // Stato del dispositivo (0=Online, 1=Batteria, 2=Vibrazione, 3=Inizio, 4=Fine)
                    );
                    const now = new Date().toISOString();
                    console.log(`[${now}][Gestionesatelliti.js] addGPSPosition chiamato con successo per deviceImei=${decodedData['Terminal Serial Number']}, lat=${decodedData['Latitude']}, long=${decodedData['Longitude']}, status=0`);
                    socket.lat=decimalLatitude;
                    socket.long=decimalLongitude;
                    socket.batteryVoltage = batteryValue;
                    socket.lastMessageTime = timedata; // Memorizza il timestamp dell'ultimo messaggio
                    socket.direction = decodedData['Direction'];
                }
            }
        } else {
            // Messaggio non valido, la stringa non inizia con * o $
        }

        // Verifica se il socket non ha già inviato il messaggio di connessione
        if (!socket.connectedMessage) {
            // Importa direttamente la funzione updateConnectionStatus dal modulo
            try {
                // Importa il modulo connection-status
                import('./connection-status.js').then(module => {
                    const now = new Date().toISOString();
                    console.log(`[${now}][Gestionesatelliti.js] Modulo connection-status importato con successo per deviceImei=${socket.deviceImei}`);

                    // Verifica se la funzione updateConnectionStatus è disponibile nel modulo
                    if (module.updateConnectionStatus && typeof module.updateConnectionStatus === 'function') {
                        module.updateConnectionStatus(true, socket.deviceImei);
                        console.log(`[${now}][Gestionesatelliti.js] updateConnectionStatus (modulo) chiamato con successo: connected=true, deviceImei=${socket.deviceImei}`);
                    } else if (global.updateConnectionStatus && typeof global.updateConnectionStatus === 'function') {
                        global.updateConnectionStatus(true, socket.deviceImei);
                        console.log(`[${now}][Gestionesatelliti.js] updateConnectionStatus (global) chiamato con successo: connected=true, deviceImei=${socket.deviceImei}`);
                    }

                    socket.connectedMessage = true;
                }).catch((error) => {
                    // Errore nell'importazione del modulo connection-status
                    console.error(`[${new Date().toISOString()}][Gestionesatelliti.js] Errore nell'importazione del modulo connection-status:`, error);
                });
            } catch (error) {
                // Errore durante l'importazione dinamica

                // Fallback: prova a usare la variabile globale
                if (global.updateConnectionStatus && typeof global.updateConnectionStatus === 'function') {
                    try {
                        const now = new Date().toISOString();
                        global.updateConnectionStatus(true, socket.deviceImei);
                        console.log(`[${now}][Gestionesatelliti.js] updateConnectionStatus (fallback global) chiamato con successo: connected=true, deviceImei=${socket.deviceImei}`);
                        socket.connectedMessage = true;
                    } catch (innerError) {
                        // Errore nel fallback
                        console.error(`[${new Date().toISOString()}][Gestionesatelliti.js] Errore nel fallback updateConnectionStatus:`, innerError);
                    }
                }
            }
        }
    });

    socket.on('close', () => {
        // Genera un messaggio di fine connessione con i valori specificati
        if (socket.deviceImei) {
            // Crea un timestamp leggermente successivo all'ultimo messaggio ricevuto
            // Se socket.lastMessageTime non è definito, usa la data corrente
            const baseTime = socket.lastMessageTime || new Date();
            const endTime = new Date(baseTime.getTime());
            endTime.setSeconds(endTime.getSeconds() + 1); // 1 secondo dopo l'ultimo messaggio

            // Usa le ultime coordinate conosciute del dispositivo
            const lastLatitude = socket.lat || 0;
            const lastLongitude = socket.long || 0;

            // Invia i dati GPS con stato dispositivo = 4 (Fine)
            if (global.addGPSPosition && typeof global.addGPSPosition === 'function') {
                try {
                    global.addGPSPosition(
                        socket.deviceImei, // ID del satellitare
                        lastLatitude,      // Ultima latitudine conosciuta
                        lastLongitude,     // Ultima longitudine conosciuta
                        0,                 // speedKmh = 0
                        1,                 // validity = 1
                        socket.direction,  // direction
                        socket.batteryVoltage, // battery level
                        endTime,           // timestamp leggermente successivo all'ultimo messaggio
                        4                  // stato del dispositivo = 4 (Fine)
                    );
                } catch (error) {
                    // Errore nella generazione del messaggio di fine connessione
                }
            }
        }

        // Importa direttamente la funzione updateConnectionStatus dal modulo
        try {
            // Importa il modulo connection-status
            import('./connection-status.js').then(module => {
                // Verifica se la funzione updateConnectionStatus è disponibile nel modulo
                const now = new Date().toISOString();
                if (module.updateConnectionStatus && typeof module.updateConnectionStatus === 'function') {
                    module.updateConnectionStatus(false, socket.deviceImei);
                    console.log(`[${now}][Gestionesatelliti.js] updateConnectionStatus (modulo) chiamato con successo per disconnessione: connected=false, deviceImei=${socket.deviceImei}`);
                } else if (global.updateConnectionStatus && typeof global.updateConnectionStatus === 'function') {
                    global.updateConnectionStatus(false, socket.deviceImei);
                    console.log(`[${now}][Gestionesatelliti.js] updateConnectionStatus (global) chiamato con successo per disconnessione: connected=false, deviceImei=${socket.deviceImei}`);
                }

                socket.connectedMessage = false;
            }).catch(() => {
                // Errore nell'importazione del modulo connection-status
            });
        } catch (error) {
            // Errore durante l'importazione dinamica

            // Fallback: prova a usare la variabile globale
            if (global.updateConnectionStatus && typeof global.updateConnectionStatus === 'function') {
                try {
                    const now = new Date().toISOString();
                    global.updateConnectionStatus(false, socket.deviceImei);
                    console.log(`[${now}][Gestionesatelliti.js] updateConnectionStatus (fallback global) chiamato con successo per disconnessione: connected=false, deviceImei=${socket.deviceImei}`);
                    socket.connectedMessage = false;
                } catch (innerError) {
                    // Errore nel fallback
                    console.error(`[${new Date().toISOString()}][Gestionesatelliti.js] Errore nel fallback updateConnectionStatus per disconnessione:`, innerError);
                }
            }
        }
    });

    socket.on('error', () => {
        // In caso di errore, generiamo anche un messaggio di fine connessione
        // ma solo se abbiamo un ID del dispositivo
        if (socket.deviceImei) {
            // Crea un timestamp leggermente successivo all'ultimo messaggio ricevuto
            // Se socket.lastMessageTime non è definito, usa la data corrente
            const baseTime = socket.lastMessageTime || new Date();
            const endTime = new Date(baseTime.getTime());
            endTime.setSeconds(endTime.getSeconds() + 1); // 1 secondo dopo l'ultimo messaggio

            // Usa le ultime coordinate conosciute del dispositivo
            const lastLatitude = socket.lat || 0;
            const lastLongitude = socket.long || 0;

            // Invia i dati GPS con stato dispositivo = 4 (Fine)
            if (global.addGPSPosition && typeof global.addGPSPosition === 'function') {
                try {
                    global.addGPSPosition(
                        socket.deviceImei, // ID del satellitare
                        lastLatitude,      // Ultima latitudine conosciuta
                        lastLongitude,     // Ultima longitudine conosciuta
                        0,                 // speedKmh = 0
                        1,                 // validity = 1
                        0,                 // direction = 0
                        socket.batteryVoltage || 0, // battery level
                        endTime,           // timestamp leggermente successivo all'ultimo messaggio
                        4                  // stato del dispositivo = 4 (Fine)
                    );
                } catch (error) {
                    // Errore nella generazione del messaggio di fine connessione per errore
                }
            }
        }
    });
});

server.listen(PORT, () => {
    // Server avviato
});
