import { useQuery, useQ<PERSON>yClient } from "@tanstack/react-query";
import { useState, useEffect, useRef } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { saveFile } from "@/lib/utils";
// Importa gli stili per l'intestazione mobile
import "../styles/mobile-header.css";

import { MapIcon, ClockIcon, BarChartIcon, LogOut, InfoIcon, DownloadIcon, ServerIcon, GaugeIcon, CompassIcon, SatelliteIcon, BatteryIcon, AlertTriangleIcon, PlayIcon, Square, BellIcon } from 'lucide-react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import Map from "@/components/Map";
import CurrentPositionCard from "@/components/CurrentPositionCard";
import PositionsTable from "@/components/PositionsTable";
import SatelliteIdForm from "@/components/SatelliteIdForm";
import SatelliteSelector from "@/components/SatelliteSelector";
import JourneySelector from "@/components/JourneySelector";
import type { Position } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import PushNotificationManager from "@/components/PushNotificationManager";
import { websocketService } from "@/lib/websocketService";

export default function Home() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user, logoutMutation } = useAuth();
  const prevSatelliteIdRef = useRef<string | null>(null);
  const [isConnected, setIsConnected] = useState(false); // Stato connessione WebSocket
  const [isSatelliteConnected, setIsSatelliteConnected] = useState(false); // Stato connessione del satellite
  const [isWebSocketError, setIsWebSocketError] = useState(false);
  const [detailsSheetOpen, setDetailsSheetOpen] = useState(false);
  const isMobile = useIsMobile();

  // Effetto per verificare il ruolo dell'utente
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-user] Verifica ruolo utente: ${user?.role || 'nessun utente'}`);
  }, [user]);

  // Stato per tenere traccia del satellitare selezionato
  const [selectedSatelliteId, setSelectedSatelliteId] = useState<string>("");

  // Stato per tenere traccia del cambio di satellitare in corso
  const [isChangingSatellite, setIsChangingSatellite] = useState<boolean>(false);

  // Stato per tenere traccia del caricamento dei dati via WebSocket
  const [isWebSocketLoading, setIsWebSocketLoading] = useState<boolean>(false);

  // Stati per tenere traccia dei valori temporanei delle date
  const [tempFromDate, setTempFromDate] = useState<string>('');
  const [tempToDate, setTempToDate] = useState<string>('');

  // Riferimenti per i timeout di debounce
  const fromDateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const toDateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Funzione per ottenere la data di inizio predefinita (00:00 del giorno corrente)
  const getDefaultFromDate = (): Date => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  };

  // Funzione per ottenere la data di fine predefinita (00:00 del giorno successivo)
  const getDefaultToDate = (): Date => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  };

  // Stato per la data di inizio dei messaggi
  const [fromDate, setFromDate] = useState<Date | null>(getDefaultFromDate());

  // Stato per la data di fine dei messaggi
  const [toDate, setToDate] = useState<Date | null>(getDefaultToDate());

  // Definisci la query key costante per coerenza
  const positionsQueryKey = ['/api/positions'];

  // Log per monitorare lo stato di abilitazione della query
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-queryState] Stato query: enabled=${!!selectedSatelliteId && !isChangingSatellite}, satelliteId=${selectedSatelliteId}, isChangingSatellite=${isChangingSatellite}`);
  }, [selectedSatelliteId, isChangingSatellite]);

  // Interrogazione iniziale per i dati - usata solo come fallback in caso di errore WebSocket
  // Inizializziamo la query con enabled=false per evitare il doppio fetching all'avvio
  const currentQueryKey = [...positionsQueryKey, selectedSatelliteId, fromDate?.toISOString(), toDate?.toISOString()];
  console.log(`[DEBUG-QUERY-KEY] Query key corrente:`, currentQueryKey);

  const { data: positions, isLoading, isError, error } = useQuery<Position[]>({
    queryKey: currentQueryKey,
    retry: 3, // Riprova fino a 3 volte in caso di errore
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Backoff esponenziale con massimo 10 secondi
    queryFn: async () => {
      console.log(`[DEBUG-QUERY-FETCH] Esecuzione queryFn con satelliteId=${selectedSatelliteId}, isChangingSatellite=${isChangingSatellite}, fromDate=${fromDate?.toISOString()}, toDate=${toDate?.toISOString()}`);

      // Se non c'è un satellitare selezionato, non fare la richiesta
      if (!selectedSatelliteId) return [];

      // Costruisci l'URL con i parametri
      let url = `${positionsQueryKey[0]}?limit=1000&satelliteId=${selectedSatelliteId}`;
      if (fromDate) {
        url += `&fromDate=${fromDate.toISOString()}`;
      }
      if (toDate) {
        url += `&toDate=${toDate.toISOString()}`;
      }

      console.log(`[DEBUG-CLIENT] Fetching positions from API: ${url}`);

      try {
        // Usa il metodo GET per recuperare i dati
        const res = await fetch(url, {
          method: 'GET',
          credentials: "include",
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Requested-With': 'XMLHttpRequest' // Indica che è una richiesta AJAX
          }
        });

        if (res.status === 401) {
          // Verifica se l'errore è dovuto al cambio di satellitare
          // Se è stato appena cambiato il satellitare, proviamo a fare un nuovo tentativo
          if (selectedSatelliteId) {
            // Verifica se l'utente è ancora autenticato
            try {
              const userCheckResponse = await fetch('/api/user', {
                method: 'GET',
                credentials: 'include',
                headers: {
                  'Cache-Control': 'no-cache, no-store, must-revalidate',
                  'Pragma': 'no-cache',
                  'Expires': '0'
                }
              });

              // Se l'utente è ancora autenticato, l'errore 401 potrebbe essere temporaneo
              if (userCheckResponse.ok) {
                throw new Error('Errore temporaneo nel caricamento dei dati. Riprova tra qualche istante.');
              } else {
                // Se l'utente non è più autenticato, reindirizza alla pagina di login
                setTimeout(() => {
                  logoutMutation.mutate();
                }, 1000);
                throw new Error('Sessione scaduta. Effettua nuovamente il login.');
              }
            } catch (checkError) {
              throw new Error('Errore nel caricamento dei dati. Riprova tra qualche istante.');
            }
          } else {
            // Se non c'è un satellitare selezionato, reindirizza alla pagina di login
            setTimeout(() => {
              logoutMutation.mutate();
            }, 1000);
            throw new Error('Sessione scaduta. Effettua nuovamente il login.');
          }
        }

        if (!res.ok) throw new Error(`Network response was not ok: ${res.status} ${res.statusText}`);

        const data = await res.json();
        console.log(`[DEBUG-QUERY-RESPONSE] Ricevuti ${data.length} dati da HTTP per satelliteId=${selectedSatelliteId}, fromDate=${fromDate?.toISOString()}, toDate=${toDate?.toISOString()}`);
        return data;
      } catch (error) {
        throw error;
      }
    },
    refetchInterval: false, // Disabilita completamente il polling
    refetchOnWindowFocus: false, // Disabilita il refetch quando la finestra ottiene il focus
    refetchOnMount: false, // Disabilita il refetch quando il componente viene montato
    staleTime: Infinity, // Considera i dati sempre freschi
    enabled: !!selectedSatelliteId // Abilitata se c'è un satellitare selezionato
  });

  // Stato per tenere traccia della posizione selezionata
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);

  // Stato per tenere traccia del viaggio selezionato
  const [selectedJourneyId, setSelectedJourneyId] = useState<number | null>(null);



  // Stato per la riproduzione delle posizioni
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const playbackTimerRef = useRef<NodeJS.Timeout | null>(null);
  const playbackIndexRef = useRef<number>(0);
  const [playbackPositions, setPlaybackPositions] = useState<Position[]>([]);


  // Tutte le posizioni disponibili con indici originali
  const positionsWithIndices = positions ? positions.map((pos) => ({
    ...pos,
    // Aggiungiamo una proprietà per tenere traccia dell'indice originale nell'array completo
    originalIndex: positions.findIndex(p => p.id === pos.id),
    // Aggiungiamo anche la lunghezza totale dell'array per calcolare correttamente il numero di posizione
    totalLength: positions.length
  })) : [];

  // Funzione per formattare la data per l'input datetime-local
  const formatDateTimeForInput = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // Funzione per validare le date (assicura che la data di fine sia successiva a quella di inizio)
  const validateDates = (start: Date | null, end: Date | null): { validStart: Date | null, validEnd: Date | null } => {
    if (!start || !end) return { validStart: start, validEnd: end };

    // Se la data di fine è antecedente a quella di inizio, aggiungi un'ora alla data di inizio
    if (end < start) {
      const correctedEnd = new Date(start.getTime());
      correctedEnd.setHours(correctedEnd.getHours() + 1);
      return { validStart: start, validEnd: correctedEnd };
    }

    return { validStart: start, validEnd: end };
  };

  // Funzione comune per aggiornare le date e richiedere nuove posizioni
  const updateDatesAndFetchPositions = (validStart: Date | null, validEnd: Date | null) => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] Aggiornamento date: fromDate=${validStart?.toISOString()}, toDate=${validEnd?.toISOString()}`);

    // Assicurati che non siamo in modalità cambio satellitare
    if (isChangingSatellite) {
      console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] Resettato isChangingSatellite durante cambio date`);
      setIsChangingSatellite(false);
    }

    // Resetta il viaggio selezionato quando cambiano le date
    setSelectedJourneyId(null);

    // Aggiorna i riferimenti alle date precedenti
    const fromDateStr = validStart?.toISOString() || null;
    const toDateStr = validEnd?.toISOString() || null;
    prevFromDateRef.current = fromDateStr;
    prevToDateRef.current = toDateStr;

    // Aggiorna gli stati delle date
    setFromDate(validStart);
    setToDate(validEnd);

    // Quando l'utente cambia la data, rimuoviamo il flag isLatestOnly dalle posizioni
    if (positions) {
      const updatedPositions = positions.map(pos => ({
        ...pos,
        isLatestOnly: false // Rimuovi il flag
      }));

      // Crea la nuova query key con le nuove date
      const newQueryKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];
      console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] Nuova query key:`, newQueryKey);

      // Aggiorna i dati nella cache di React Query
      queryClient.setQueryData(newQueryKey, updatedPositions);
    }

    // Richiedi le posizioni tramite WebSocket
    if (websocketService.isConnected() && selectedSatelliteId) {
      console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] Invio richiesta posizioni con nuove date via WebSocket`);
      // Imposta lo stato di caricamento WebSocket
      setIsWebSocketLoading(true);

      // Stampa i parametri che stiamo inviando
      console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] Parametri richiesta: satelliteId=${selectedSatelliteId}, fromDate=${validStart?.toISOString()}, toDate=${validEnd?.toISOString()}`);

      websocketService.requestPositions({
        satelliteId: selectedSatelliteId,
        fromDate: validStart,
        toDate: validEnd
      });
    } else {
      console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] WebSocket non disponibile, utilizzo HTTP fallback`);
      // Attiva la query HTTP come fallback
      if (selectedSatelliteId) {
        console.log(`[${now}][Home.tsx][updateDatesAndFetchPositions] Attivazione query HTTP come fallback`);

        // Crea la nuova query key con le nuove date
        const newQueryKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];

        queryClient.setQueryDefaults(newQueryKey, {
          enabled: true
        });

        // Invalida la query per forzare un nuovo fetch
        queryClient.invalidateQueries({
          queryKey: newQueryKey,
          exact: true
        });
      }
    }
  };

  // Funzione per gestire il cambio della data di inizio con debounce
  const handleFromDateChange = (newDate: Date | null) => {
    // Valida le date
    const { validStart, validEnd } = validateDates(newDate, toDate);

    // Aggiorna gli stati locali
    setFromDate(validStart);

    // Se la data di fine è stata corretta, aggiornala
    if (validEnd !== toDate) {
      setToDate(validEnd);
    }

    // Aggiorna le date e richiedi nuove posizioni
    updateDatesAndFetchPositions(validStart, validEnd);
  };

  // Funzione per gestire il cambio della data di fine con debounce
  const handleToDateChange = (newDate: Date | null) => {
    // Valida le date
    const { validStart, validEnd } = validateDates(fromDate, newDate);

    // Aggiorna gli stati locali
    setToDate(validEnd);

    // Se la data di inizio è stata corretta, aggiornala
    if (validStart !== fromDate) {
      setFromDate(validStart);
    }

    // Aggiorna le date e richiedi nuove posizioni
    updateDatesAndFetchPositions(validStart, validEnd);
  };

  // Funzione per gestire il cambio della data di inizio con debounce
  const handleFromDateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Salva il valore temporaneo
    setTempFromDate(e.target.value);

    // Se c'è un timeout precedente, cancellalo
    if (fromDateTimeoutRef.current) {
      clearTimeout(fromDateTimeoutRef.current);
    }

    // Non fare nulla se il campo è vuoto
    if (!e.target.value) return;

    // Crea un nuovo timeout
    fromDateTimeoutRef.current = setTimeout(() => {
      const newDate = new Date(e.target.value);
      if (!isNaN(newDate.getTime())) {
        handleFromDateChange(newDate);
      }
    }, 1000); // Attendi 1 secondo prima di aggiornare
  };

  // Funzione per gestire il cambio della data di fine con debounce
  const handleToDateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Salva il valore temporaneo
    setTempToDate(e.target.value);

    // Se c'è un timeout precedente, cancellalo
    if (toDateTimeoutRef.current) {
      clearTimeout(toDateTimeoutRef.current);
    }

    // Non fare nulla se il campo è vuoto
    if (!e.target.value) return;

    // Crea un nuovo timeout
    toDateTimeoutRef.current = setTimeout(() => {
      const newDate = new Date(e.target.value);
      if (!isNaN(newDate.getTime())) {
        handleToDateChange(newDate);
      }
    }, 1000); // Attendi 1 secondo prima di aggiornare
  };



  // Rimuoviamo lo stato non utilizzato
  // const [wsJustOpened, setWsJustOpened] = useState(false);

  // Funzione per gestire il cambio di satellitare
  const handleSatelliteChange = (newSatelliteId: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][handleSatelliteChange] Richiesta cambio satellitare a: ${newSatelliteId}`);

    // Se il satellitare è lo stesso, non fare nulla
    if (selectedSatelliteId === newSatelliteId) {
      console.log(`[${now}][Home.tsx][handleSatelliteChange] Satellitare già selezionato: ${newSatelliteId}`);

      // Anche se il satellite è lo stesso, invia comunque una richiesta WebSocket
      // per assicurarsi che il server sappia quale satellite il client sta monitorando
      if (websocketService.isConnected() && isConnected) {
        console.log(`[${now}][Home.tsx][handleSatelliteChange] Invio richiesta WebSocket per aggiornare il satelliteId sul server`);

        // Assicurati che le date siano definite per evitare problemi di sincronizzazione
        const safeFromDate = fromDate || getDefaultFromDate();
        const safeToDate = toDate || getDefaultToDate();

        websocketService.requestPositions({
          satelliteId: newSatelliteId,
          fromDate: safeFromDate,
          toDate: safeToDate
        });

        // Richiedi anche lo stato di connessione del satellite
        websocketService.requestConnectionStatus(newSatelliteId);
      }

      return;
    }

    console.log(`[${now}][Home.tsx][handleSatelliteChange] Cambio satellitare da ${selectedSatelliteId || 'vuoto'} a ${newSatelliteId}`);

    // Imposta lo stato di cambio satellitare
    setIsChangingSatellite(true);
    console.log(`[${now}][Home.tsx][handleSatelliteChange] Impostato isChangingSatellite=true`);

    // Resetta la posizione e il viaggio selezionati
    setSelectedPosition(null);
    setSelectedJourneyId(null);
    console.log(`[${now}][Home.tsx][handleSatelliteChange] Reset posizione e viaggio selezionati`);

    // Imposta il nuovo satellitare selezionato
    setSelectedSatelliteId(newSatelliteId);
    console.log(`[${now}][Home.tsx][handleSatelliteChange] Nuovo satellitare impostato: ${newSatelliteId}`);

    // Ottieni i valori correnti delle date
    const fromDateStr = fromDate?.toISOString() || null;
    const toDateStr = toDate?.toISOString() || null;

    // Aggiorna la query key corrente
    const currentKey = [...positionsQueryKey, newSatelliteId, fromDateStr, toDateStr];
    console.log(`[${now}][Home.tsx][handleSatelliteChange] Aggiornamento query key corrente:`, currentKey);

    // Verifica se ci sono dati nella cache per questa query key
    const cachedData = queryClient.getQueryData<Position[]>(currentKey);

    // Se ci sono dati nella cache, usali per aggiornare l'UI immediatamente
    if (cachedData !== undefined && cachedData.length > 0) {
      console.log(`[${now}][Home.tsx][handleSatelliteChange] Dati già in cache (${cachedData.length} posizioni), utilizzo dati esistenti`);

      // Imposta direttamente i dati nella cache senza forzare un refetch
      // Questo evita il doppio aggiornamento della UI
      queryClient.setQueryData(currentKey, cachedData);

      // Resetta lo stato di caricamento
      setIsWebSocketLoading(false);

      // Resetta lo stato di cambio satellitare
      if (isChangingSatellite) {
        console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Resetto isChangingSatellite dopo utilizzo dati dalla cache`);
        setIsChangingSatellite(false);
      }
    }

    // Verifica se il WebSocket è effettivamente connesso
    const wsIsActive = websocketService.isConnected() && isConnected;
    console.log(`[${now}][Home.tsx][handleSatelliteChange] Stato WebSocket: isConnected=${isConnected}, websocketService.isConnected()=${websocketService.isConnected()}, wsIsActive=${wsIsActive}`);

    if (wsIsActive) {
      // Se il WebSocket è attivo, usa quello per richiedere le posizioni
      console.log(`[${now}][Home.tsx][handleSatelliteChange] WebSocket attivo, invio richiesta posizioni`);

      // Disabilita la query HTTP per evitare il doppio caricamento
      queryClient.setQueryDefaults(currentKey, {
        enabled: false
      });

      // Imposta lo stato di caricamento WebSocket solo se non ci sono dati in cache
      if (!cachedData || cachedData.length === 0) {
        setIsWebSocketLoading(true);
      }

      // Assicurati che le date siano definite per evitare problemi di sincronizzazione
      const safeFromDate = fromDate || getDefaultFromDate();
      const safeToDate = toDate || getDefaultToDate();

      websocketService.requestPositions({
        satelliteId: newSatelliteId,
        fromDate: safeFromDate,
        toDate: safeToDate
      });

      // Richiedi anche lo stato di connessione del satellite
      console.log(`[${now}][Home.tsx][handleSatelliteChange] Richiesta stato connessione per satelliteId=${newSatelliteId}`);
      websocketService.requestConnectionStatus(newSatelliteId);
    } else {
      // Se il WebSocket non è attivo, usa HTTP come fallback
      console.log(`[${now}][Home.tsx][handleSatelliteChange] WebSocket non attivo, utilizzo HTTP fallback`);

      // Imposta lo stato di caricamento solo se non ci sono dati in cache
      if (!cachedData || cachedData.length === 0) {
        setIsWebSocketLoading(true);
      }

      // Attiva la query HTTP come fallback
      console.log(`[${now}][Home.tsx][handleSatelliteChange] Attivazione query HTTP come fallback`);
      queryClient.setQueryDefaults(currentKey, {
        enabled: true
      });

      // Invalida la query per forzare un nuovo fetch
      queryClient.invalidateQueries({
        queryKey: currentKey,
        exact: true
      }).then(() => {
        // Assicurati che lo stato di caricamento sia resettato dopo il fetch
        setIsWebSocketLoading(false);

        // Resetta lo stato di cambio satellitare
        if (isChangingSatellite) {
          console.log(`[${now}][Home.tsx][handleSatelliteChange] Resetto isChangingSatellite dopo fetch HTTP completato`);
          setIsChangingSatellite(false);
        }
      }).catch(() => {
        setIsWebSocketLoading(false);

        // Resetta lo stato di cambio satellitare anche in caso di errore
        if (isChangingSatellite) {
          console.log(`[${now}][Home.tsx][handleSatelliteChange] Resetto isChangingSatellite dopo errore fetch HTTP`);
          setIsChangingSatellite(false);
        }
      });
    }

    // Non resettiamo più isChangingSatellite qui con setTimeout
    // Verrà resettato quando le posizioni saranno effettivamente ricevute in handlePositionsUpdate
    console.log(`[${now}][Home.tsx][handleSatelliteChange] isChangingSatellite rimarrà true fino a quando le posizioni saranno ricevute`);
  };

  // Riferimenti alle funzioni di callback per evitare che vengano ricreate ad ogni render
  const handlePositionsUpdateRef = useRef<(positions: Position[]) => void>();
  const handleConnectionStatusRef = useRef<(data: { connected: boolean }) => void>();
  const handleConnectedRef = useRef<() => void>();
  const handleDisconnectedRef = useRef<() => void>();
  const handleErrorRef = useRef<() => void>();

  // Inizializza i riferimenti alle funzioni di callback una sola volta
  useEffect(() => {
    // Configura gli event listener per il WebSocket service
    handlePositionsUpdateRef.current = (positions: Position[]) => {
      const eventNow = new Date().toISOString();
      console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Ricevuto positions-update con ${positions.length} posizioni`);

      // Resetta lo stato di caricamento WebSocket
      setIsWebSocketLoading(false);

      // Ottieni il satelliteId corrente
      // Usa il satelliteId dalla prima posizione o quello selezionato
      const satelliteId = positions.length > 0 ? positions[0].satelliteId : selectedSatelliteId;

      // Se il satelliteId è diverso da quello selezionato e non siamo in modalità cambio satellitare,
      // aggiorna il satelliteId selezionato
      if (satelliteId && satelliteId !== selectedSatelliteId && !isChangingSatellite) {
        console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Aggiornamento satelliteId selezionato da ${selectedSatelliteId || 'vuoto'} a ${satelliteId}`);
        setSelectedSatelliteId(satelliteId);
      }

      // Ottieni i valori correnti delle date
      // Usa le date dalle posizioni ricevute se disponibili, altrimenti usa quelle correnti
      let fromDateStr = fromDate?.toISOString() || null;
      let toDateStr = toDate?.toISOString() || null;

      // Se ci sono posizioni, estrai il range di date dai dati ricevuti
      if (positions.length > 0) {
        // Ordina le posizioni per timestamp
        const sortedPositions = [...positions].sort((a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );

        // Estrai la prima e l'ultima data
        const firstDate = new Date(sortedPositions[0].timestamp);
        const lastDate = new Date(sortedPositions[sortedPositions.length - 1].timestamp);

        // Arrotonda le date a mezzanotte
        firstDate.setHours(0, 0, 0, 0);
        lastDate.setHours(23, 59, 59, 999);

        // Aggiorna le stringhe di data
        fromDateStr = firstDate.toISOString();
        toDateStr = lastDate.toISOString();

        console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Date estratte dai dati: fromDate=${fromDateStr}, toDate=${toDateStr}`);
      }

      // Costruisci la chiave di query completa
      const queryKey = [...positionsQueryKey, satelliteId, fromDateStr, toDateStr];
      console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Aggiornamento cache con queryKey:`, queryKey);
      console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] SatelliteId usato: ${satelliteId}, selectedSatelliteId: ${selectedSatelliteId || 'vuoto'}`);

      // Verifica se i dati sono già presenti nella cache e se sono identici
      const cachedData = queryClient.getQueryData<Position[]>(queryKey);
      const currentPositionsHash = calculatePositionsHash(positions);
      const cachedPositionsHash = calculatePositionsHash(cachedData);
      const dataIsIdentical = currentPositionsHash === cachedPositionsHash && cachedPositionsHash !== '';

      if (dataIsIdentical) {
        console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] I dati ricevuti sono identici a quelli in cache, nessun aggiornamento necessario`);

        // Anche se i dati sono identici, verifica se siamo in modalità cambio satellitare
        // e resetta lo stato se necessario
        if (isChangingSatellite && satelliteId === selectedSatelliteId) {
          console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Resetto isChangingSatellite anche se i dati sono identici`);
          setIsChangingSatellite(false);
        }

        return; // Esci dalla funzione senza aggiornare la UI
      }

      console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] I dati sono cambiati, aggiornamento cache e UI`);

      // Aggiorna i dati nella cache senza invalidare la query
      queryClient.setQueryData(queryKey, positions);

      // Aggiorna anche la query con la chiave corrente
      const currentQueryKey = [...positionsQueryKey, selectedSatelliteId || satelliteId, fromDate?.toISOString() || null, toDate?.toISOString() || null];
      console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Aggiornamento anche della query corrente:`, currentQueryKey);
      queryClient.setQueryData(currentQueryKey, positions);

      // Disabilita la query HTTP quando riceviamo dati dal WebSocket (sia con posizioni che senza)
      console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Disabilitazione query HTTP dopo ricezione dati WebSocket`);
      queryClient.setQueryDefaults(queryKey, {
        enabled: false
      });

      // Forza un aggiornamento diretto della cache per tutte le possibili query key
      // Questo garantisce che i componenti vedano i dati aggiornati
      const allPossibleKeys = [
        // Query key con le date estratte dai dati
        [...positionsQueryKey, satelliteId, fromDateStr, toDateStr],
        // Query key con le date correnti
        [...positionsQueryKey, satelliteId, fromDate?.toISOString() || null, toDate?.toISOString() || null],
        // Query key con il satelliteId selezionato e le date estratte
        [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr],
        // Query key con il satelliteId selezionato e le date correnti
        [...positionsQueryKey, selectedSatelliteId, fromDate?.toISOString() || null, toDate?.toISOString() || null]
      ];

      // Aggiorna tutte le possibili query key
      allPossibleKeys.forEach(key => {
        console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Aggiornamento cache per key:`, key);
        queryClient.setQueryData(key, positions);
      });

      // Aggiorna la posizione selezionata se ci sono posizioni
      if (positions.length > 0) {
        if (!selectedPosition || !positions.some(p => p.id === selectedPosition.id)) {
          console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Aggiornamento posizione selezionata alla prima posizione`);
          // Ordina le posizioni per timestamp (dalla più recente alla più vecchia)
          const sortedPositions = [...positions].sort((a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          );
          setSelectedPosition(sortedPositions[0]);
        }
      } else {
        // Se non ci sono posizioni, resetta la posizione selezionata
        console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Nessuna posizione disponibile, reset posizione selezionata`);
        setSelectedPosition(null);
      }

      // Verifica se siamo in modalità cambio satellitare e se il satelliteId corrisponde a quello selezionato
      if (isChangingSatellite && satelliteId === selectedSatelliteId) {
        console.log(`[${eventNow}][Home.tsx][handlePositionsUpdate] Resetto isChangingSatellite dopo ricezione posizioni per satelliteId=${satelliteId}`);
        setIsChangingSatellite(false);
      }
    };

    handleConnectionStatusRef.current = (data: { connected: boolean }) => {
      const eventNow = new Date().toISOString();
      console.log(`[${eventNow}][Home.tsx][handleConnectionStatus] Ricevuto connection-status: ${data.connected}`);

      // Aggiorna lo stato di connessione del satellite (questo è lo stato del satellite, non del WebSocket)
      console.log(`[${eventNow}][Home.tsx][handleConnectionStatus] Aggiornamento stato connessione satellite da ${isSatelliteConnected} a ${data.connected}`);
      setIsSatelliteConnected(data.connected);

      // Se lo stato della connessione WebSocket è cambiato
      if (isConnected !== data.connected) {
        console.log(`[${eventNow}][Home.tsx][handleConnectionStatus] Cambio stato connessione WebSocket da ${isConnected} a ${data.connected}`);

        // Aggiorna lo stato della connessione WebSocket
        setIsConnected(data.connected);
        setIsWebSocketError(false);

        // Se la connessione è stata persa e c'è un satellite selezionato, attiva il fallback HTTP
        if (!data.connected && selectedSatelliteId) {
          console.log(`[${eventNow}][Home.tsx][handleConnectionStatus] Connessione persa, attivazione fallback HTTP per satelliteId=${selectedSatelliteId}`);

          // Ottieni i valori correnti delle date
          const fromDateStr = fromDate?.toISOString() || null;
          const toDateStr = toDate?.toISOString() || null;

          // Costruisci la chiave di query completa
          const currentKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];

          // Attiva la query HTTP come fallback
          queryClient.setQueryDefaults(currentKey, {
            enabled: true
          });

          // Invalida la query per forzare un nuovo fetch
          queryClient.invalidateQueries({
            queryKey: currentKey,
            exact: true
          });
        }
        // Se la connessione è stata ripristinata e c'è un satellite selezionato, disattiva il fallback HTTP
        else if (data.connected && selectedSatelliteId) {
          console.log(`[${eventNow}][Home.tsx][handleConnectionStatus] Connessione ripristinata, disattivazione fallback HTTP per satelliteId=${selectedSatelliteId}`);

          // Ottieni i valori correnti delle date
          const fromDateStr = fromDate?.toISOString() || null;
          const toDateStr = toDate?.toISOString() || null;

          // Costruisci la chiave di query completa
          const currentKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];

          // Disattiva la query HTTP
          queryClient.setQueryDefaults(currentKey, {
            enabled: false
          });

          // Richiedi le posizioni tramite WebSocket
          const safeFromDate = fromDate || getDefaultFromDate();
          const safeToDate = toDate || getDefaultToDate();

          websocketService.requestPositions({
            satelliteId: selectedSatelliteId,
            fromDate: safeFromDate,
            toDate: safeToDate
          });
        }
      }

      console.log(`[${eventNow}][Home.tsx][handleConnectionStatus] Stato aggiornato: isSatelliteConnected=${data.connected}, isConnected=${isConnected}, isWebSocketError=${isWebSocketError}`);
    };

    handleConnectedRef.current = () => {
      const eventNow = new Date().toISOString();
      console.log(`[${eventNow}][Home.tsx][handleConnected] WebSocket connesso`);
      setIsWebSocketError(false);
      setIsConnected(true);
      console.log(`[${eventNow}][Home.tsx][handleConnected] Stato connessione aggiornato: isConnected=true, isWebSocketError=false`);

      // Disabilita la query HTTP quando il WebSocket è connesso
      if (selectedSatelliteId) {
        // Ottieni i valori correnti delle date
        const fromDateStr = fromDate?.toISOString() || null;
        const toDateStr = toDate?.toISOString() || null;

        // Costruisci la chiave di query completa
        const queryKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];

        console.log(`[${eventNow}][Home.tsx][handleConnected] Disabilitazione query HTTP con queryKey:`, queryKey);
        queryClient.setQueryDefaults(queryKey, {
          enabled: false
        });

        // Verifica se ci sono dati nella cache per questa query key
        const cachedData = queryClient.getQueryData<Position[]>(queryKey);

        // Se non ci sono dati nella cache o sono vuoti, imposta lo stato di caricamento
        if (!cachedData || cachedData.length === 0) {
          setIsWebSocketLoading(true);
        }

        // Assicurati che le date siano definite per evitare problemi di sincronizzazione
        const safeFromDate = fromDate || getDefaultFromDate();
        const safeToDate = toDate || getDefaultToDate();

        console.log(`[${eventNow}][Home.tsx][handleConnected] Satellitare selezionato, invio richiesta posizioni: ${selectedSatelliteId}`);
        websocketService.requestPositions({
          satelliteId: selectedSatelliteId,
          fromDate: safeFromDate,
          toDate: safeToDate
        });

        // Richiedi anche lo stato di connessione del satellite
        console.log(`[${eventNow}][Home.tsx][handleConnected] Richiesta stato connessione per satelliteId=${selectedSatelliteId}`);
        websocketService.requestConnectionStatus(selectedSatelliteId);
      } else {
        console.log(`[${eventNow}][Home.tsx][handleConnected] Nessun satellitare selezionato, nessuna richiesta inviata`);
      }
    };

    handleDisconnectedRef.current = () => {
      const eventNow = new Date().toISOString();
      console.log(`[${eventNow}][Home.tsx][handleDisconnected] WebSocket disconnesso`);

      // Aggiorna immediatamente lo stato di connessione
      setIsConnected(false);
      console.log(`[${eventNow}][Home.tsx][handleDisconnected] Stato connessione aggiornato: isConnected=false`);

      // Se la disconnessione persiste, abilita il fallback HTTP
      setTimeout(() => {
        const timeoutNow = new Date().toISOString();
        if (!websocketService.isConnected()) {
          console.log(`[${timeoutNow}][Home.tsx][handleDisconnected] Passaggio a polling HTTP dopo disconnessione persistente`);
          setIsWebSocketError(true);
          console.log(`[${timeoutNow}][Home.tsx][handleDisconnected] Stato errore WebSocket aggiornato: isWebSocketError=true`);

          // Attiva la query HTTP come fallback
          if (selectedSatelliteId) {
            // Ottieni i valori correnti delle date
            const fromDateStr = fromDate?.toISOString() || null;
            const toDateStr = toDate?.toISOString() || null;

            // Costruisci la chiave di query completa
            const queryKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];

            console.log(`[${timeoutNow}][Home.tsx][handleDisconnected] Attivazione query HTTP come fallback con queryKey:`, queryKey);

            // Verifica se ci sono dati nella cache per questa query key
            const cachedData = queryClient.getQueryData<Position[]>(queryKey);

            // Attiva la query HTTP
            queryClient.setQueryDefaults(queryKey, {
              enabled: true
            });

            // Invalida la query per forzare un nuovo fetch solo se non ci sono dati in cache
            if (!cachedData || cachedData.length === 0) {
              console.log(`[${timeoutNow}][Home.tsx][handleDisconnected] Nessun dato in cache, forzatura fetch HTTP`);
              queryClient.invalidateQueries({
                queryKey: queryKey,
                exact: true
              });
            } else {
              console.log(`[${timeoutNow}][Home.tsx][handleDisconnected] Dati già in cache (${cachedData.length} posizioni), nessun fetch HTTP immediato`);
            }
          }
        } else {
          console.log(`[${timeoutNow}][Home.tsx][handleDisconnected] WebSocket riconnesso, nessun passaggio a polling HTTP`);
        }
      }, 2000); // Ridotto a 2 secondi per una risposta più rapida
    };

    handleErrorRef.current = () => {
      const eventNow = new Date().toISOString();
      console.log(`[${eventNow}][Home.tsx][handleError] Errore WebSocket`);
      setIsWebSocketError(true);
      setIsConnected(false);
      console.log(`[${eventNow}][Home.tsx][handleError] Stato aggiornato: isWebSocketError=true, isConnected=false`);

      // Attiva la query HTTP come fallback in caso di errore WebSocket
      if (selectedSatelliteId) {
        // Ottieni i valori correnti delle date
        const fromDateStr = fromDate?.toISOString() || null;
        const toDateStr = toDate?.toISOString() || null;

        // Costruisci la chiave di query completa
        const queryKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];

        console.log(`[${eventNow}][Home.tsx][handleError] Attivazione query HTTP come fallback con queryKey:`, queryKey);

        // Verifica se ci sono dati nella cache per questa query key
        const cachedData = queryClient.getQueryData<Position[]>(queryKey);

        // Attiva la query HTTP
        queryClient.setQueryDefaults(queryKey, {
          enabled: true
        });

        // Invalida la query per forzare un nuovo fetch solo se non ci sono dati in cache
        if (!cachedData || cachedData.length === 0) {
          console.log(`[${eventNow}][Home.tsx][handleError] Nessun dato in cache, forzatura fetch HTTP`);
          queryClient.invalidateQueries({
            queryKey: queryKey,
            exact: true
          });
        } else {
          console.log(`[${eventNow}][Home.tsx][handleError] Dati già in cache (${cachedData.length} posizioni), nessun fetch HTTP immediato`);
        }
      }

      // Tenta di riconnettere il WebSocket
      setTimeout(() => {
        if (!websocketService.isConnected()) {
          console.log(`[${eventNow}][Home.tsx][handleError] Tentativo di riconnessione WebSocket dopo errore`);
          websocketService.connect();
        }
      }, 5000); // Attendi 5 secondi prima di tentare la riconnessione
    };
  }, []);

  // Configura il WebSocket service
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-websocket] Inizializzazione WebSocket service`);

    if (!user) {
      console.log(`[${now}][Home.tsx][useEffect-websocket] Nessun utente, uscita`);
      return;
    }

    // Verifica se i listener sono già stati aggiunti
    const hasListeners = websocketService.hasEventListener('positions-update');
    if (hasListeners) {
      console.log(`[${now}][Home.tsx][useEffect-websocket] Event listeners già presenti, nessuna azione necessaria`);

      // Anche se i listener sono già presenti, verifichiamo se il WebSocket è connesso
      // e se c'è un satelliteId selezionato, inviamo una richiesta per assicurarci
      // che il server sappia quale satellite il client sta monitorando
      if (websocketService.isConnected() && selectedSatelliteId) {
        console.log(`[${now}][Home.tsx][useEffect-websocket] WebSocket già connesso, invio richiesta posizioni per satelliteId=${selectedSatelliteId}`);

        // Assicurati che le date siano definite per evitare problemi di sincronizzazione
        const safeFromDate = fromDate || getDefaultFromDate();
        const safeToDate = toDate || getDefaultToDate();

        websocketService.requestPositions({
          satelliteId: selectedSatelliteId,
          fromDate: safeFromDate,
          toDate: safeToDate
        });

        // Richiedi anche lo stato di connessione del satellite
        websocketService.requestConnectionStatus(selectedSatelliteId);
      }

      return;
    }

    // Aggiungi gli event listener
    console.log(`[${now}][Home.tsx][useEffect-websocket] Aggiunta event listeners WebSocket`);
    websocketService.addEventListener('positions-update', handlePositionsUpdateRef.current!);
    websocketService.addEventListener('connection-status', handleConnectionStatusRef.current!);
    websocketService.addEventListener('connected', handleConnectedRef.current!);
    websocketService.addEventListener('disconnected', handleDisconnectedRef.current!);
    websocketService.addEventListener('error', handleErrorRef.current!);

    // Connetti il WebSocket
    console.log(`[${now}][Home.tsx][useEffect-websocket] Avvio connessione WebSocket`);
    websocketService.connect();

    // Cleanup function
    return () => {
      // Non rimuoviamo più gli event listener per mantenere la connessione persistente
      // e evitare il rimontaggio quando cambiano le date
      console.log(`[${now}][Home.tsx][useEffect-websocket-cleanup] WebSocket mantenuto connesso con tutti i listener`);
    };
  }, [user, selectedSatelliteId, fromDate, toDate]);



  // Riferimenti per tenere traccia delle date precedenti
  const prevFromDateRef = useRef<string | null>(null);
  const prevToDateRef = useRef<string | null>(null);

  // Effetto per aggiornare la UI quando cambiano le date
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-dateChange] Verifica cambiamento date`);

    if (!selectedSatelliteId) {
      console.log(`[${now}][Home.tsx][useEffect-dateChange] Nessun satellitare selezionato, uscita`);
      return;
    }

    const fromDateStr = fromDate?.toISOString() || null;
    const toDateStr = toDate?.toISOString() || null;

    // Verifica se sono cambiate le date
    const datesChanged = prevFromDateRef.current !== fromDateStr || prevToDateRef.current !== toDateStr;

    console.log(`[${now}][Home.tsx][useEffect-dateChange] Stato cambiamento: datesChanged=${datesChanged}`);
    console.log(`[${now}][Home.tsx][useEffect-dateChange] Valori: fromDate=${fromDateStr}, toDate=${toDateStr}`);
    console.log(`[${now}][Home.tsx][useEffect-dateChange] Precedenti: prevFromDate=${prevFromDateRef.current}, prevToDate=${prevToDateRef.current}`);

    if (datesChanged) {
      console.log(`[${now}][Home.tsx][useEffect-dateChange] Date cambiate, aggiornamento riferimenti`);
      prevFromDateRef.current = fromDateStr;
      prevToDateRef.current = toDateStr;

      // Aggiorna la query key corrente
      const currentKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];
      console.log(`[${now}][Home.tsx][useEffect-dateChange] Aggiornamento query key corrente:`, currentKey);

      // Forza un refetch per aggiornare la UI
      queryClient.refetchQueries({
        queryKey: currentKey,
        exact: true
      }).then(() => {
        console.log(`[${now}][Home.tsx][useEffect-dateChange] Refetch completato con successo`);
      }).catch(error => {
        console.error(`[${now}][Home.tsx][useEffect-dateChange] Errore durante il refetch:`, error);
      });
    }
  }, [fromDate, toDate, selectedSatelliteId, queryClient]);

  // Effetto per richiedere le posizioni quando cambia il satellitare
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Verifica cambiamento satellitare`);

    if (!selectedSatelliteId || !user) {
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Nessun satellitare o utente, uscita`);
      return;
    }

    // Verifica se è cambiato il satellitare
    const satelliteChanged = prevSatelliteIdRef.current !== selectedSatelliteId;
    const fromDateStr = fromDate?.toISOString() || null;
    const toDateStr = toDate?.toISOString() || null;

    console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Stato cambiamento: satelliteChanged=${satelliteChanged}`);
    console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Valori: satelliteId=${selectedSatelliteId}, precedente=${prevSatelliteIdRef.current || 'vuoto'}`);

    // Aggiorna il riferimento al satelliteId corrente
    if (satelliteChanged) {
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Satellitare cambiato, aggiornamento riferimenti`);
      prevSatelliteIdRef.current = selectedSatelliteId;
      prevFromDateRef.current = fromDateStr;
      prevToDateRef.current = toDateStr;
    }

    // Indipendentemente dal fatto che il satellite sia cambiato o meno,
    // invia sempre una richiesta WebSocket per assicurarsi che il server
    // sappia quale satellite il client sta monitorando
    if (websocketService.isConnected() && !isChangingSatellite) {
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] WebSocket connesso, invio richiesta posizioni per satelliteId=${selectedSatelliteId}`);
      // Imposta lo stato di caricamento WebSocket
      setIsWebSocketLoading(true);

      // Assicurati che le date siano definite per evitare problemi di sincronizzazione
      const safeFromDate = fromDate || getDefaultFromDate();
      const safeToDate = toDate || getDefaultToDate();

      websocketService.requestPositions({
        satelliteId: selectedSatelliteId,
        fromDate: safeFromDate,
        toDate: safeToDate
      });

      // Richiedi anche lo stato di connessione del satellite
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Richiesta stato connessione per satelliteId=${selectedSatelliteId}`);
      websocketService.requestConnectionStatus(selectedSatelliteId);
    }

    // Aggiorna la query key corrente
    const currentKey = [...positionsQueryKey, selectedSatelliteId, fromDateStr, toDateStr];
    console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Aggiornamento query key corrente:`, currentKey);

    // Verifica se ci sono dati nella cache per questa query key
    const cachedData = queryClient.getQueryData<Position[]>(currentKey);

    if (cachedData && cachedData.length > 0) {
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Dati già in cache (${cachedData.length} posizioni), utilizzo dati esistenti`);

      // Forza un refetch per aggiornare la UI
      queryClient.refetchQueries({
        queryKey: currentKey,
        exact: true
      }).then(() => {
        console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Refetch completato con successo`);
        setIsWebSocketLoading(false);
      }).catch(error => {
        console.error(`[${now}][Home.tsx][useEffect-satelliteChange] Errore durante il refetch:`, error);
        setIsWebSocketLoading(false);
      });
    } else if (!websocketService.isConnected()) {
      // Se non ci sono dati nella cache e il WebSocket non è connesso, usa HTTP come fallback
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] WebSocket non connesso o nessun dato in cache, utilizzo HTTP fallback`);

      // Attiva la query HTTP come fallback
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Attivazione query HTTP come fallback`);
      queryClient.setQueryDefaults(currentKey, {
        enabled: true
      });

      // Invalida la query per forzare un nuovo fetch
      queryClient.invalidateQueries({
        queryKey: currentKey,
        exact: true
      }).then(() => {
        console.log(`[${now}][Home.tsx][useEffect-satelliteChange] Fetch HTTP completato con successo`);
        setIsWebSocketLoading(false);
      }).catch(error => {
        console.error(`[${now}][Home.tsx][useEffect-satelliteChange] Errore durante il fetch HTTP:`, error);
        setIsWebSocketLoading(false);
      });
    } else if (isChangingSatellite) {
      console.log(`[${now}][Home.tsx][useEffect-satelliteChange] In modalità cambio satellitare, nessuna azione necessaria`);
    }
  // Manteniamo fromDate e toDate nelle dipendenze per assicurarci che il WebSocket venga aggiornato quando cambiano
  }, [selectedSatelliteId, user, queryClient, isChangingSatellite, fromDate, toDate]);

  // Riferimento per tenere traccia dell'hash delle posizioni
  const positionsHashRef = useRef<string>('');

  // Funzione per calcolare un hash semplice delle posizioni
  const calculatePositionsHash = (positions: Position[] | undefined): string => {
    if (!positions || positions.length === 0) return '';

    // Prendiamo gli ID, le coordinate e lo stato per un confronto più accurato
    // Questo ci permette di rilevare cambiamenti nelle posizioni anche se gli ID sono gli stessi
    const positionData = positions.map(p => ({
      id: p.id,
      lat: p.latitude,
      lng: p.longitude,
      status: p.deviceStatus,
      timestamp: p.timestamp
    }));

    // Ordiniamo per ID per garantire un confronto coerente
    positionData.sort((a, b) => a.id - b.id);

    // Creiamo una stringa che rappresenta i dati
    return positionData.map(p => `${p.id}:${p.lat},${p.lng},${p.status},${p.timestamp}`).join('|');
  };

  // Monitora i cambiamenti nei dati delle posizioni
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Monitoraggio cambiamenti posizioni`);

    if (positions) {
      // Calcola l'hash delle posizioni attuali
      const currentHash = calculatePositionsHash(positions);

      // Verifica se le posizioni sono effettivamente cambiate
      const positionsChanged = currentHash !== positionsHashRef.current;

      // Aggiorna il riferimento all'hash
      positionsHashRef.current = currentHash;

      // Se le posizioni non sono cambiate, evita elaborazioni inutili
      if (!positionsChanged && selectedPosition) {
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Posizioni non cambiate, nessuna azione necessaria`);
        return;
      }

      console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Aggiornamento positions con ${positions.length} elementi, isChangingSatellite=${isChangingSatellite}, satelliteId=${selectedSatelliteId}`);

      // Aggiorna i dati nella cache di React Query per assicurarsi che siano disponibili
      const currentKey = [...positionsQueryKey, selectedSatelliteId, fromDate?.toISOString() || null, toDate?.toISOString() || null];
      console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Aggiornamento cache con queryKey:`, currentKey);

      // Verifica se i dati nella cache sono diversi da quelli correnti
      const cachedData = queryClient.getQueryData<Position[]>(currentKey);
      if (!cachedData || cachedData.length !== positions.length) {
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Dati in cache diversi, aggiornamento cache`);
        queryClient.setQueryData(currentKey, positions);

        // Forza un refetch per aggiornare la UI, ma solo se necessario
        if (positionsChanged) {
          queryClient.refetchQueries({
            queryKey: currentKey,
            exact: true
          }).then(() => {
            console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Refetch completato con successo`);
          }).catch(error => {
            console.error(`[${now}][Home.tsx][useEffect-positionsMonitor] Errore durante il refetch:`, error);
          });
        }
      }

      if (positions.length > 0) {
        // Ordina le posizioni per timestamp (dalla più recente alla più vecchia)
        const sortedPositions = [...positions].sort((a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );

        const firstTimestamp = new Date(sortedPositions[0].timestamp).toISOString();
        const lastTimestamp = new Date(sortedPositions[sortedPositions.length - 1].timestamp).toISOString();
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Range date: da ${lastTimestamp} a ${firstTimestamp}`);
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Filtri: fromDate=${fromDate?.toISOString()}, toDate=${toDate?.toISOString()}`);

        // Log dei primi e ultimi ID per debug
        const firstIds = sortedPositions.slice(0, Math.min(3, sortedPositions.length)).map(p => p.id).join(', ');
        const lastIds = sortedPositions.slice(Math.max(0, sortedPositions.length - 3)).map(p => p.id).join(', ');
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Primi IDs (più recenti): ${firstIds}`);
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Ultimi IDs (più vecchi): ${lastIds}`);

        // Se non c'è una posizione selezionata o la posizione selezionata non è più presente nelle nuove posizioni,
        // seleziona la prima posizione (la più recente)
        if (!selectedPosition || !positions.some(p => p.id === selectedPosition.id)) {
          console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Aggiornamento posizione selezionata alla prima posizione`);
          setSelectedPosition(sortedPositions[0]);
        }
      } else {
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Nessuna posizione disponibile`);

        // Se non ci sono posizioni, assicurati che la posizione selezionata sia null
        if (selectedPosition) {
          console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Reset posizione selezionata perché non ci sono posizioni disponibili`);
          setSelectedPosition(null);
        }

        // Assicurati che lo stato di caricamento sia resettato
        setIsWebSocketLoading(false);
      }
    } else {
      console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Positions non definito (null/undefined)`);

      // Se non ci sono posizioni ma c'è un satellitare selezionato, richiedi le posizioni
      if (selectedSatelliteId && !isChangingSatellite) {
        console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] Nessuna posizione ma satellitare selezionato, richiesta posizioni`);

        // Se il WebSocket è connesso, richiedi le posizioni
        if (websocketService.isConnected()) {
          console.log(`[${now}][Home.tsx][useEffect-positionsMonitor] WebSocket connesso, invio richiesta posizioni`);
          setIsWebSocketLoading(true);

          // Assicurati che le date siano definite per evitare problemi di sincronizzazione
          const safeFromDate = fromDate || getDefaultFromDate();
          const safeToDate = toDate || getDefaultToDate();

          websocketService.requestPositions({
            satelliteId: selectedSatelliteId,
            fromDate: safeFromDate,
            toDate: safeToDate
          });
        }
      }
    }
  // Rimossi fromDate e toDate dalle dipendenze perché non sono necessari per questo effetto
  // Questo evita che l'effetto venga eseguito quando cambiano solo le date
  }, [positions, selectedSatelliteId, isChangingSatellite, selectedPosition, queryClient]);

  // Usa useEffect per impostare la posizione selezionata quando i dati sono caricati
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Verifica impostazione posizione selezionata`);

    if (positions && positions.length > 0) {
      // Ordina le posizioni per timestamp (dalla più recente alla più vecchia)
      const sortedPositions = [...positions].sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      if (!selectedPosition) {
        console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Posizioni disponibili (${positions.length}) e nessuna posizione selezionata, imposto la più recente`);
        setSelectedPosition(sortedPositions[0]);
        console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Impostata posizione selezionata a id=${sortedPositions[0].id}, timestamp=${new Date(sortedPositions[0].timestamp).toISOString()}`);
      } else if (!positions.some(p => p.id === selectedPosition.id)) {
        // La posizione selezionata non è più presente nelle nuove posizioni
        console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Posizione selezionata (id=${selectedPosition.id}) non più presente nelle nuove posizioni, imposto la più recente`);
        setSelectedPosition(sortedPositions[0]);
        console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Impostata posizione selezionata a id=${sortedPositions[0].id}, timestamp=${new Date(sortedPositions[0].timestamp).toISOString()}`);
      } else {
        console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Posizione già selezionata: id=${selectedPosition.id}, timestamp=${new Date(selectedPosition.timestamp).toISOString()}`);
      }
    } else if (!positions || positions.length === 0) {
      console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Nessuna posizione disponibile`);

      // Se non ci sono posizioni, resetta la posizione selezionata
      if (selectedPosition) {
        console.log(`[${now}][Home.tsx][useEffect-selectedPosition] Reset posizione selezionata`);
        setSelectedPosition(null);
      }
    }
  }, [positions, selectedPosition]);

  // Riferimento per tenere traccia del numero di posizioni precedenti
  const prevPositionsLengthRef = useRef<number>(0);

  // Effetto separato per monitorare l'arrivo di nuovi messaggi durante la riproduzione
  useEffect(() => {
    // Se non è attiva la riproduzione, aggiorna solo il riferimento
    if (!isPlaying) {
      prevPositionsLengthRef.current = positions?.length || 0;
      return;
    }

    // Verifica se sono arrivati nuovi messaggi (solo se il numero è aumentato)
    const currentLength = positions?.length || 0;
    if (currentLength > prevPositionsLengthRef.current && prevPositionsLengthRef.current > 0) {
      // Ferma la riproduzione
      if (playbackTimerRef.current) {
        clearInterval(playbackTimerRef.current);
        playbackTimerRef.current = null;
      }
      setIsPlaying(false);

      // Mostra un messaggio all'utente
      toast({
        title: "Riproduzione interrotta",
        description: "Sono arrivati nuovi dati durante la riproduzione.",
        variant: "default"
      });
    }

    // Aggiorna il riferimento
    prevPositionsLengthRef.current = currentLength;
  }, [positions, isPlaying]);



  // Gestisce la selezione di una posizione sulla mappa
  const handlePositionSelect = (position: Position) => {
    setSelectedPosition(position);
  };

  // Gestisce la selezione di un viaggio
  const handleJourneySelect = (journeyId: number | null, startPositionId: number | null) => {
    console.log(`[DEBUG-JOURNEY] Selezione viaggio: journeyId=${journeyId}, startPositionId=${startPositionId}`);
    setSelectedJourneyId(journeyId);

    if (positions && positions.length > 0) {
      // Se è stato selezionato un viaggio specifico, seleziona la posizione di inizio
      if (journeyId !== null && startPositionId !== null) {
        const startPosition = positions.find(pos => pos.id === startPositionId);
        if (startPosition) {
          // Rimossi log di debug
          setSelectedPosition(startPosition);
        } else {
          console.log(`ERRORE: Posizione di inizio non trovata per id=${startPositionId}`);
        }
      }
      // Se è stato selezionato "Tutti i viaggi", seleziona l'ultima posizione arrivata (indice 0)
      else if (journeyId === null) {
        // Rimossi log di debug
        setSelectedPosition(positions[0]);
      }
    }
  };

  // Funzione per gestire l'esportazione KML
  const handleExportKML = async () => {
    try {
      if (!positions || positions.length === 0) {
        toast({
          title: "Errore",
          description: "Nessuna posizione disponibile da esportare",
          variant: "destructive"
        });
        return;
      }

      // Mostra un toast di caricamento
      toast({
        title: "Esportazione in corso",
        description: "Attendere il completamento del download..."
      });

      // Costruisci l'URL con i parametri
      let url = `/api/positions/export?format=kml&satelliteId=${positions[0]?.satelliteId || ''}`;

      // Aggiungi il parametro fromDate se presente
      if (fromDate) {
        url += `&fromDate=${fromDate.toISOString()}`;
      }

      // Aggiungi il parametro toDate se presente
      if (toDate) {
        url += `&toDate=${toDate.toISOString()}`;
      }

      // Effettua la richiesta al server
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include'
      });

      // Verifica se la risposta è valida
      if (!response.ok) {
        throw new Error(`Errore durante l'esportazione: ${response.status} ${response.statusText}`);
      }

      // Ottieni il nome del file dall'header Content-Disposition
      let filename = `positions_${positions[0]?.satelliteId || 'all'}.kml`;
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // Ottieni i dati dalla risposta
      const data = await response.text();

      // Conta le posizioni (approssimazione contando i Placemark)
      const matches = data.match(/<Placemark>/g);
      const positionsCount = matches ? matches.length : 0;

      // Salva il file
      saveFile(data, filename, 'application/vnd.google-earth.kml+xml');

      // Mostra un toast di successo con il conteggio delle posizioni
      toast({
        title: "Esportazione completata",
        description: `File ${filename} salvato con successo (${positionsCount-1} posizioni)`
      });
    } catch (error) {
      console.error("Errore durante l'esportazione KML:", error);
      toast({
        title: "Errore",
        description: "Impossibile esportare i dati in formato KML",
        variant: "destructive"
      });
    }
  };

  // Funzione per gestire la riproduzione delle posizioni
  const handlePlayback = () => {
    // Se è già in riproduzione, ferma la riproduzione
    if (isPlaying) {
      if (playbackTimerRef.current) {
        clearInterval(playbackTimerRef.current);
        playbackTimerRef.current = null;
      }
      setIsPlaying(false);
      // Ripristina la visualizzazione di tutte le posizioni
      setPlaybackPositions([]);
      return;
    }

    // Verifica che ci siano posizioni da riprodurre
    if (!positions || positions.length === 0) return;

    // Salva il numero attuale di posizioni prima di iniziare la riproduzione
    prevPositionsLengthRef.current = positions.length;

    // Ordina le posizioni dalla più vecchia alla più recente
    const sortedPositions = [...positions].sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // Crea una copia locale delle posizioni ordinate per evitare problemi con aggiornamenti esterni
    const positionsSnapshot = [...sortedPositions];

    // Determina l'indice di partenza per la riproduzione
    let startIndex = 0;

    // Se c'è una posizione selezionata e non è l'ultima posizione nella tabella
    if (selectedPosition && selectedPosition.id !== positions[0].id) {
      // Trova l'indice della posizione selezionata nell'array ordinato
      const selectedIndex = positionsSnapshot.findIndex(pos => pos.id === selectedPosition.id);

      // Se la posizione selezionata è stata trovata, inizia da lì
      if (selectedIndex !== -1) {
        startIndex = selectedIndex;
      }
    }

    // Imposta l'indice di partenza
    playbackIndexRef.current = startIndex;

    // Inizia con la posizione di partenza
    setPlaybackPositions([positionsSnapshot[startIndex]]);

    // Seleziona la posizione di partenza
    setSelectedPosition(positionsSnapshot[startIndex]);

    // Imposta lo stato di riproduzione
    setIsPlaying(true);

    // Determiniamo sempre a quale viaggio appartiene la posizione di partenza
    // e aggiorniamo il viaggio selezionato per garantire la coerenza dei colori
    // Rimossi log di debug

    // Determiniamo a quale viaggio appartiene la posizione di partenza
    if (positionsSnapshot[startIndex]) {
      // Importiamo la funzione identifyJourneyForPosition da Map.tsx
      // Questa è una duplicazione temporanea, in un refactoring futuro si potrebbe spostare in un file utils
      const identifyJourneyForPosition = (position: Position, allPositions: Position[]): number => {
        // Ordina tutte le posizioni cronologicamente
        const chronologicalPositions = [...allPositions].sort((a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );

        // Trova tutte le posizioni di inizio (deviceStatus = 3)
        const startPositions = chronologicalPositions.filter(pos => pos.deviceStatus === 3);

        // Se non ci sono posizioni di inizio, restituisci 1 (primo viaggio)
        if (startPositions.length === 0) return 1;

        // Per le posizioni di inizio, troviamo il loro indice nell'array delle posizioni di inizio
        if (position.deviceStatus === 3) {
          const index = startPositions.findIndex(pos => pos.id === position.id);
          if (index !== -1) {
            return index + 1; // 1-based
          }
        }

        // Timestamp della posizione da verificare
        const positionTime = new Date(position.timestamp).getTime();

        // Trova il viaggio a cui appartiene la posizione
        for (let i = 0; i < startPositions.length; i++) {
          const startTime = new Date(startPositions[i].timestamp).getTime();

          // Se è l'ultimo viaggio o la posizione è prima dell'inizio del prossimo viaggio
          const nextStartPosition = startPositions[i + 1];
          const nextStartTime = nextStartPosition ? new Date(nextStartPosition.timestamp).getTime() : Infinity;

          if (positionTime >= startTime && positionTime < nextStartTime) {
            // Restituisce l'indice del viaggio (1-based)
            return i + 1;
          }
        }

        // Se la posizione è precedente al primo viaggio, restituisci il primo viaggio
        if (positionTime < new Date(startPositions[0].timestamp).getTime()) {
          return 1;
        }

        // Se non appartiene a nessun viaggio specifico, restituisci l'ultimo viaggio
        return startPositions.length;
      };

      // Determina il viaggio della posizione di partenza
      const journeyId = identifyJourneyForPosition(positionsSnapshot[startIndex], positions);
      // Rimossi log di debug

      // Imposta il viaggio selezionato
      // Forziamo l'aggiornamento del viaggio selezionato anche se è già lo stesso
      // Prima lo impostiamo a null e poi al valore corretto per forzare il re-render
      setSelectedJourneyId(null);

      // Utilizziamo setTimeout per assicurarci che l'aggiornamento a null venga elaborato
      setTimeout(() => {
        // Rimossi log di debug
        setSelectedJourneyId(journeyId);
      }, 0);
    }

    // Avvia il timer per riprodurre le posizioni una alla volta
    playbackTimerRef.current = setInterval(() => {
      // Incrementa l'indice
      playbackIndexRef.current++;

      // Se abbiamo raggiunto la fine, ferma la riproduzione
      if (playbackIndexRef.current >= positionsSnapshot.length) {
        if (playbackTimerRef.current) {
          clearInterval(playbackTimerRef.current);
          playbackTimerRef.current = null;
        }
        setIsPlaying(false);
        // Ripristina la visualizzazione di tutte le posizioni
        setPlaybackPositions([]);
        return;
      }

      // Aggiungi la posizione corrente all'array delle posizioni di riproduzione
      const currentPosition = positionsSnapshot[playbackIndexRef.current];
      setPlaybackPositions(prev => [...prev, currentPosition]);

      // Seleziona la posizione corrente
      setSelectedPosition(currentPosition);

      // Se la posizione corrente è una posizione di inizio (deviceStatus = 3),
      // aggiorniamo il viaggio selezionato per mantenere la coerenza dei colori
      if (currentPosition.deviceStatus === 3) {
        // Determina il viaggio della posizione corrente
        const identifyJourneyForPosition = (position: Position, allPositions: Position[]): number => {
          // Ordina tutte le posizioni cronologicamente
          const chronologicalPositions = [...allPositions].sort((a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );

          // Trova tutte le posizioni di inizio (deviceStatus = 3)
          const startPositions = chronologicalPositions.filter(pos => pos.deviceStatus === 3);

          // Se non ci sono posizioni di inizio, restituisci 1 (primo viaggio)
          if (startPositions.length === 0) return 1;

          // Per le posizioni di inizio, troviamo il loro indice nell'array delle posizioni di inizio
          if (position.deviceStatus === 3) {
            const index = startPositions.findIndex(pos => pos.id === position.id);
            if (index !== -1) {
              return index + 1; // 1-based
            }
          }

          // Timestamp della posizione da verificare
          const positionTime = new Date(position.timestamp).getTime();

          // Trova il viaggio a cui appartiene la posizione
          for (let i = 0; i < startPositions.length; i++) {
            const startTime = new Date(startPositions[i].timestamp).getTime();

            // Se è l'ultimo viaggio o la posizione è prima dell'inizio del prossimo viaggio
            const nextStartPosition = startPositions[i + 1];
            const nextStartTime = nextStartPosition ? new Date(nextStartPosition.timestamp).getTime() : Infinity;

            if (positionTime >= startTime && positionTime < nextStartTime) {
              // Restituisce l'indice del viaggio (1-based)
              return i + 1;
            }
          }

          // Se la posizione è precedente al primo viaggio, restituisci il primo viaggio
          if (positionTime < new Date(startPositions[0].timestamp).getTime()) {
            return 1;
          }

          // Se non appartiene a nessun viaggio specifico, restituisci l'ultimo viaggio
          return startPositions.length;
        };

        // Determina il viaggio della posizione corrente
        const journeyId = identifyJourneyForPosition(currentPosition, positions);
        // Rimossi log di debug

        // Imposta il viaggio selezionato
        // Forziamo l'aggiornamento del viaggio selezionato anche se è già lo stesso
        // Prima lo impostiamo a null e poi al valore corretto per forzare il re-render
        setSelectedJourneyId(null);

        // Utilizziamo setTimeout per assicurarci che l'aggiornamento a null venga elaborato
        setTimeout(() => {
          // Rimossi log di debug
          setSelectedJourneyId(journeyId);
        }, 0);
      }
    }, 1000); // Intervallo di 1 secondo tra le posizioni
  };

  // Pulizia dei timer quando il componente viene smontato
  useEffect(() => {
    return () => {
      // Pulizia del timer di playback
      if (playbackTimerRef.current) {
        clearInterval(playbackTimerRef.current);
      }

      // Pulizia dei timer di debounce
      if (fromDateTimeoutRef.current) {
        clearTimeout(fromDateTimeoutRef.current);
      }

      if (toDateTimeoutRef.current) {
        clearTimeout(toDateTimeoutRef.current);
      }
    };
  }, []);



  return (
    <div className="h-screen bg-gradient-to-b from-teal-50 to-teal-100 font-sans flex flex-col">
      {/* Header */}
      <header className="bg-teal-600 shadow-md border-b border-teal-700">
        <div className="w-[98%] mx-auto py-2">
          {isMobile ? (
            // Layout mobile a due colonne
            <div className="mobile-app-header-container">
              {/* Colonna sinistra: Logo, nome utente, stato connessione */}
              <div className="mobile-app-header-left-column">
                {/* Logo e titolo */}
                <div className="mobile-app-header-logo">
                  <img src="/logo.png" alt="Logo" />
                  <h1 className="text-white ecotrac-title">
                    ECOTrac
                  </h1>
                </div>

                {/* Nome utente e icone di stato connessione */}
                <div className="mobile-app-header-icons-container">
                  {/* Nome utente */}
                  <span className="mobile-app-header-username text-white">
                    {user?.name || 'Utente'}
                  </span>

                  {/* Stato connessione - solo icone */}
                  <div className="mobile-app-header-connection">
                    <SatelliteIcon className={`connection-icon ${isSatelliteConnected ? 'text-green-500' : 'text-red-500'}`} />
                  </div>
                  <div className="mobile-app-header-connection">
                    <ServerIcon className={`connection-icon ${isConnected ? 'text-green-500' : 'text-red-500'}`} />
                  </div>
                </div>
              </div>

              {/* Colonna destra: Pulsanti, selettore satellitare (ordine invertito) */}
              <div className="mobile-app-header-right-column">
                {/* Pulsanti di azione */}
                <div className="mobile-app-header-actions">
                  <PushNotificationManager className="mobile-app-header-button" />
                  {/* Pulsante Admin rimosso nella modalità mobile */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => logoutMutation.mutate()}
                    disabled={logoutMutation.isPending}
                    className="bg-teal-500 text-white hover:bg-teal-600 border-teal-400 mobile-app-header-button"
                  >
                    <LogOut className="h-3 w-3 mr-1" />
                    Esci
                  </Button>
                </div>

                {/* Selettore satellitare */}
                {user?.satelliteId && (
                  <div className="mobile-app-header-satellite-selector">
                    <SatelliteSelector
                      user={user}
                      onSatelliteSelect={handleSatelliteChange}
                      currentSatelliteId={selectedSatelliteId}
                    />
                  </div>
                )}
              </div>
            </div>
          ) : (
            // Layout desktop originale
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <img src="/logo.png" alt="Logo" className="w-10 h-10 mr-3" />
                <h1 className="text-4xl md:text-5xl font-bold text-white ecotrac-title">
                  ECOTrac
                </h1>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <SatelliteIcon className={`h-6 w-6 ${isSatelliteConnected ? 'text-green-500' : 'text-red-500'}`} />
                    <ServerIcon className={`h-5 w-5 ${isConnected ? 'text-green-500' : 'text-red-500'}`} />
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-white">
                    {user?.name || 'Utente'}
                  </span>

                  {/* Selettore satellitare */}
                  {user?.satelliteId && (
                    <SatelliteSelector
                      user={user}
                      onSatelliteSelect={handleSatelliteChange}
                      currentSatelliteId={selectedSatelliteId}
                    />
                  )}

                  <div className="flex items-center gap-2">
                    <PushNotificationManager />
                    {user?.role === "admin" && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = "/admin"}
                        className="bg-teal-500 text-white hover:bg-teal-600 border-teal-400"
                      >
                        <ServerIcon className="h-4 w-4 mr-1" />
                        Admin
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => logoutMutation.mutate()}
                      disabled={logoutMutation.isPending}
                      className="bg-teal-500 text-white hover:bg-teal-600 border-teal-400"
                    >
                      <LogOut className="h-4 w-4 mr-1" />
                      Esci
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="w-[98%] mx-auto flex-grow overflow-hidden">
        {/* Layout responsive */}
        <div className="flex flex-col gap-0.5 h-full">
          {/* Layout per desktop e tablet */}
          {!isMobile ? (
            <>
              {/* Utilizziamo ResizablePanelGroup per il layout verticale principale */}
              <ResizablePanelGroup
                direction="vertical"
                className="h-full"
              >
                {/* Pannello superiore con mappa e dettagli */}
                <ResizablePanel
                  defaultSize={55}
                  minSize={30}
                >
                  {/* Pannello orizzontale per mappa e dettagli */}
                  <ResizablePanelGroup
                    direction="horizontal"
                    className="h-full"
                  >
                    {/* Pannello della mappa */}
                    <ResizablePanel
                      defaultSize={80}
                      minSize={50}
                      className="h-full"
                    >
                      {/* Map Section */}
                      <Card className="rounded-xl shadow-lg border-teal-300 overflow-hidden h-full flex flex-col bg-white">
                        <CardHeader className="bg-gradient-to-r from-teal-100 to-white py-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <MapIcon className="h-5 w-5 text-teal-600 mr-2" />
                              <CardTitle className="text-lg font-medium text-gray-800">
                                Mappa Posizioni
                              </CardTitle>
                            </div>
                            {positions && positions.length > 0 && (
                              <JourneySelector
                                positions={positions}
                                onJourneySelect={handleJourneySelect}
                                selectedJourneyId={selectedJourneyId}
                              />
                            )}
                          </div>
                        </CardHeader>
                        <CardContent className="p-2 flex-grow flex flex-col">
                          {isLoading || isWebSocketLoading ? (
                            <div className="w-full h-full">
                              <Skeleton className="h-full w-full rounded-lg" />
                            </div>
                          ) : !user?.satelliteId ? (
                            <div className="w-full h-full rounded-lg bg-gray-50 flex items-center justify-center">
                              <div className="w-full max-w-md">
                                <SatelliteIdForm userId={user?.id || 0} />
                              </div>
                            </div>
                          ) : isError ? (
                            <div className="w-full h-full rounded-lg bg-red-50 flex flex-col items-center justify-center p-4">
                              <p className="text-red-500 font-medium mb-2">Errore nel caricamento dei dati</p>
                              <p className="text-red-500 text-center">
                                {String(error).includes('Sessione scaduta')
                                  ? 'La tua sessione è scaduta. Verrai reindirizzato alla pagina di login...'
                                  : String(error)}
                              </p>
                              {String(error).includes('Sessione scaduta') && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-4 bg-red-100 text-red-700 hover:bg-red-200 border-red-300"
                                  onClick={() => logoutMutation.mutate()}
                                >
                                  Vai alla pagina di login
                                </Button>
                              )}
                            </div>
                          ) : positions && positions.length > 0 ? (
                            <div className="w-full h-full">
                              <Map
                                positions={positionsWithIndices}
                                selectedPosition={selectedPosition}
                                onPositionSelect={handlePositionSelect}
                                isPlaying={isPlaying}
                                playbackPositions={playbackPositions}
                                selectedJourneyId={selectedJourneyId}
                              />
                            </div>
                          ) : (
                            <div className="w-full h-full rounded-lg bg-gray-50 flex items-center justify-center">
                              <p className="text-gray-500">Nessuna posizione disponibile per questo satellite</p>
                            </div>
                          )}

                          {/* Legenda rimossa per massimizzare lo spazio della mappa */}
                        </CardContent>
                      </Card>
                    </ResizablePanel>

                    {/* Handle per il ridimensionamento orizzontale */}
                    <ResizableHandle withHandle className="bg-teal-100 hover:bg-teal-200" />

                    {/* Pannello dei dettagli - visibile solo su desktop */}
                    <ResizablePanel
                      defaultSize={20}
                      minSize={15}
                      className="hidden lg:block"
                    >
                      {/* Current Position Card */}
                      <Card className="rounded-xl shadow-lg border-teal-300 overflow-hidden h-full bg-white">
                        <CardHeader className="bg-gradient-to-r from-teal-100 to-white py-2">
                          <div className="flex items-center">
                            <BarChartIcon className="h-5 w-5 text-teal-600 mr-2" />
                            <CardTitle className="text-lg font-medium text-gray-800">
                              Dettagli
                            </CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent className="p-2 overflow-y-auto scrollbar-visible" style={{ maxHeight: "calc(100% - 40px)" }}>
                          {isLoading || isWebSocketLoading ? (
                            <Skeleton className="h-24 w-full rounded-lg" />
                          ) : positions && positions.length > 0 && selectedPosition ? (
                            <CurrentPositionCard
                              position={selectedPosition}
                              isSelected={true}
                            />
                          ) : (
                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                              <p className="text-center text-gray-500">Nessuna posizione disponibile</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </ResizablePanel>
                  </ResizablePanelGroup>
                </ResizablePanel>

                {/* Handle per il ridimensionamento verticale */}
                <ResizableHandle withHandle className="bg-teal-100 hover:bg-teal-200" />

                {/* Pannello inferiore con tabella e dettagli su tablet */}
                <ResizablePanel
                  defaultSize={45}
                  minSize={20}
                >
                  <div className="w-full h-full flex flex-col md:flex-row gap-0.5">
                    {/* Dettagli su tablet (visibile solo su tablet) */}
                    <div className="hidden md:block lg:hidden md:w-1/3 h-full">
                      {/* Current Position Card */}
                      <Card className="rounded-xl shadow-lg border-teal-300 overflow-hidden h-full bg-white">
                        <CardHeader className="bg-gradient-to-r from-teal-100 to-white py-2">
                          <div className="flex items-center">
                            <BarChartIcon className="h-5 w-5 text-teal-600 mr-2" />
                            <CardTitle className="text-lg font-medium text-gray-800">
                              Dettagli
                            </CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent className="p-2 overflow-y-auto scrollbar-visible" style={{ maxHeight: "calc(100% - 40px)" }}>
                          {isLoading || isWebSocketLoading ? (
                            <Skeleton className="h-24 w-full rounded-lg" />
                          ) : positions && positions.length > 0 && selectedPosition ? (
                            <CurrentPositionCard
                              position={selectedPosition}
                              isSelected={true}
                            />
                          ) : (
                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                              <p className="text-center text-gray-500">Nessuna posizione disponibile</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>

                    {/* Tabella */}
                    <div className="w-full md:w-2/3 lg:w-full h-full">
                      {/* Table Section */}
                      <Card className="rounded-xl shadow-lg border-teal-300 overflow-hidden h-full flex flex-col bg-white">
                        <CardHeader className="bg-gradient-to-r from-teal-100 to-white py-1">
                          <div className="flex items-center justify-between flex-wrap gap-2">
                            <div className="flex items-center">
                              <ClockIcon className="h-4 w-4 text-teal-600 mr-2" />
                              <CardTitle className="text-base font-medium text-gray-800">
                                Cronologia Posizioni
                              </CardTitle>
                            </div>
                            <div className="flex items-center gap-2 flex-wrap">
                              {positions && positions.length > 0 && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-1 text-xs h-7 px-2 py-1 border-teal-300 text-teal-700 hover:bg-teal-50 mr-2"
                                    onClick={handleExportKML}
                                    title="Esporta dati in formato KML (Google Earth/Maps)"
                                  >
                                    <DownloadIcon className="h-3 w-3" />
                                    KML
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className={`flex items-center gap-1 text-xs h-7 px-2 py-1 mr-2 ${isPlaying ? 'bg-red-50 border-red-300 text-red-700 hover:bg-red-100' : 'border-teal-300 text-teal-700 hover:bg-teal-50'}`}
                                    onClick={handlePlayback}
                                    title={isPlaying ? "Ferma la riproduzione" : "Riproduci le posizioni in sequenza"}
                                  >
                                    {isPlaying ? <Square className="h-3 w-3" /> : <PlayIcon className="h-3 w-3" />}
                                    {isPlaying ? "STOP" : "PLAY"}
                                  </Button>
                                </>
                              )}
                              <div className="flex items-center">
                                <label htmlFor="from-date-input" className="mr-1 text-gray-700 font-medium text-xs">Da:</label>
                                <input
                                  id="from-date-input"
                                  type="datetime-local"
                                  value={tempFromDate || (fromDate ? formatDateTimeForInput(fromDate) : '')}
                                  onChange={handleFromDateInputChange}
                                  className="w-40 px-2 py-1 border border-gray-300 rounded text-xs"
                                />
                              </div>
                              <div className="flex items-center">
                                <label htmlFor="to-date-input" className="mr-1 text-gray-700 font-medium text-xs">A:</label>
                                <input
                                  id="to-date-input"
                                  type="datetime-local"
                                  value={tempToDate || (toDate ? formatDateTimeForInput(toDate) : '')}
                                  onChange={handleToDateInputChange}
                                  className="w-40 px-2 py-1 border border-gray-300 rounded text-xs"
                                />
                              </div>
                            </div>
                          </div>
                          {/* Etichette delle colonne rimosse come richiesto */}
                        </CardHeader>
                        <CardContent className="p-1 flex-grow overflow-hidden" style={{ display: "flex", flexDirection: "column", flexGrow: 1 }}>
                          {isLoading || isWebSocketLoading ? (
                            <div className="overflow-x-auto h-full">
                              <Skeleton className="h-full w-full rounded-lg" />
                            </div>
                          ) : positions && positions.length > 0 ? (
                            <div className="h-full flex-grow overflow-hidden" style={{ display: "flex", flexDirection: "column", flexGrow: 1 }}>
                              <PositionsTable
                                positions={positions}
                                selectedPosition={selectedPosition}
                                onPositionSelect={handlePositionSelect}
                                fromDate={fromDate}
                                onFromDateChange={handleFromDateChange}
                                toDate={toDate}
                                onToDateChange={handleToDateChange}
                              />
                            </div>
                          ) : (
                            <div className="overflow-x-auto">
                              <p className="text-center text-gray-500 py-8">Nessuna posizione nella cronologia</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            </>
          ) : (
            /* Layout per mobile */
            <>
              {/* Mappa (occupa il 60% dell'altezza) */}
              <div className="h-[60%] mb-0.5">
                <Card className="rounded-xl shadow-lg border-teal-300 overflow-hidden h-full flex flex-col bg-white">
                  <CardHeader className="bg-gradient-to-r from-teal-100 to-white py-2 px-3">
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center justify-between">
                        {positions && positions.length > 0 && (
                          <div className="flex-grow mr-2" style={{ maxWidth: "calc(100% - 100px)" }}>
                            <JourneySelector
                              positions={positions}
                              onJourneySelect={handleJourneySelect}
                              selectedJourneyId={selectedJourneyId}
                            />
                          </div>
                        )}
                        {selectedPosition && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1 text-sm min-w-[90px] mobile-journey-details-button"
                            onClick={() => setDetailsSheetOpen(true)}
                          >
                            <InfoIcon className="h-4 w-4" />
                            Dettagli
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-1 flex-grow flex flex-col">
                    {isLoading || isWebSocketLoading ? (
                      <div className="w-full h-full">
                        <Skeleton className="h-full w-full rounded-lg" />
                      </div>
                    ) : !user?.satelliteId ? (
                      <div className="w-full h-full rounded-lg bg-gray-50 flex items-center justify-center">
                        <div className="w-full max-w-md">
                          <SatelliteIdForm userId={user?.id || 0} />
                        </div>
                      </div>
                    ) : isError ? (
                      <div className="w-full h-full rounded-lg bg-red-50 flex flex-col items-center justify-center p-4">
                        <p className="text-red-500 font-medium mb-2">Errore nel caricamento dei dati</p>
                        <p className="text-red-500 text-center">
                          {String(error).includes('Sessione scaduta')
                            ? 'La tua sessione è scaduta. Verrai reindirizzato alla pagina di login...'
                            : String(error)}
                        </p>
                        {String(error).includes('Sessione scaduta') && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-4 bg-red-100 text-red-700 hover:bg-red-200 border-red-300"
                            onClick={() => logoutMutation.mutate()}
                          >
                            Vai alla pagina di login
                          </Button>
                        )}
                      </div>
                    ) : positions && positions.length > 0 ? (
                      <div className="w-full h-full">
                        <Map
                          positions={positionsWithIndices}
                          selectedPosition={selectedPosition}
                          onPositionSelect={handlePositionSelect}
                          isPlaying={isPlaying}
                          playbackPositions={playbackPositions}
                          selectedJourneyId={selectedJourneyId}
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full rounded-lg bg-gray-50 flex items-center justify-center">
                        <p className="text-gray-500">Nessuna posizione disponibile per questo satellite</p>
                      </div>
                    )}

                    {/* Legenda rimossa per massimizzare lo spazio della mappa */}
                  </CardContent>
                </Card>
              </div>

              {/* Tabella (occupa il 40% dell'altezza) */}
              <div className="h-[40%]">
                <Card className="rounded-xl shadow-lg border-teal-300 overflow-hidden h-full flex flex-col bg-white">
                  <CardHeader className="bg-gradient-to-r from-teal-100 to-white py-1">
                    {/* Nuova struttura per l'intestazione mobile */}
                    <div className="mobile-header-container">
                      {/* Colonna sinistra con i pulsanti */}
                      <div className="mobile-header-left">
                        {positions && positions.length > 0 && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mobile-button flex items-center gap-1 text-xs h-7 px-2 py-1 border-teal-300 text-teal-700 hover:bg-teal-50"
                              onClick={handleExportKML}
                              title="Esporta dati in formato KML (Google Earth/Maps)"
                            >
                              <DownloadIcon className="h-3 w-3 mr-1" />
                              KML
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className={`mobile-button flex items-center gap-1 text-xs h-7 px-2 py-1 ${isPlaying ? 'bg-red-50 border-red-300 text-red-700 hover:bg-red-100' : 'border-teal-300 text-teal-700 hover:bg-teal-50'}`}
                              onClick={handlePlayback}
                              title={isPlaying ? "Ferma la riproduzione" : "Riproduci le posizioni in sequenza"}
                            >
                              {isPlaying ? <Square className="h-3 w-3 mr-1" /> : <PlayIcon className="h-3 w-3 mr-1" />}
                              {isPlaying ? "STOP" : "PLAY"}
                            </Button>
                          </>
                        )}
                      </div>

                      {/* Colonna destra con gli input delle date */}
                      <div className="mobile-header-right">
                        <div className="flex items-center">
                          <label htmlFor="mobile-from-date-input" className="mr-1 text-gray-700 font-medium text-xs">Da:</label>
                          <input
                            id="mobile-from-date-input"
                            type="datetime-local"
                            value={tempFromDate || (fromDate ? formatDateTimeForInput(fromDate) : '')}
                            onChange={handleFromDateInputChange}
                            className="mobile-date-input px-2 py-1 border border-gray-300 rounded text-xs"
                          />
                        </div>
                        <div className="flex items-center">
                          <label htmlFor="mobile-to-date-input" className="mr-1 text-gray-700 font-medium text-xs">A:</label>
                          <input
                            id="mobile-to-date-input"
                            type="datetime-local"
                            value={tempToDate || (toDate ? formatDateTimeForInput(toDate) : '')}
                            onChange={handleToDateInputChange}
                            className="mobile-date-input px-2 py-1 border border-gray-300 rounded text-xs"
                          />
                        </div>
                      </div>
                    </div>
                    {/* Etichette delle colonne rimosse come richiesto */}
                  </CardHeader>
                  <CardContent className="p-1 flex-grow overflow-hidden" style={{ display: "flex", flexDirection: "column", flexGrow: 1 }}>
                    {isLoading || isWebSocketLoading ? (
                      <div className="overflow-x-auto h-full">
                        <Skeleton className="h-full w-full rounded-lg" />
                      </div>
                    ) : positions && positions.length > 0 ? (
                      <div className="h-full flex-grow overflow-hidden" style={{ display: "flex", flexDirection: "column", flexGrow: 1 }}>
                        <PositionsTable
                          positions={positions}
                          selectedPosition={selectedPosition}
                          onPositionSelect={handlePositionSelect}
                          fromDate={fromDate}
                          onFromDateChange={handleFromDateChange}
                          toDate={toDate}
                          onToDateChange={handleToDateChange}
                          isMobile={true}
                        />
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <p className="text-center text-gray-500 py-8">Nessuna posizione nella cronologia</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Sheet per i dettagli su mobile */}
              <Sheet open={detailsSheetOpen} onOpenChange={setDetailsSheetOpen}>
                <SheetContent className="w-full sm:max-w-md overflow-hidden flex flex-col mobile-sheet-content">
                  <SheetHeader>
                    <SheetTitle>Dettagli Posizione</SheetTitle>
                  </SheetHeader>
                  {selectedPosition && (
                    <div className="mt-4 overflow-y-auto flex-grow" style={{ maxHeight: "calc(100vh - 120px)" }}>
                      <CurrentPositionCard
                        position={selectedPosition}
                        isSelected={true}
                        className="mobile-popup-details"
                      />
                    </div>
                  )}
                </SheetContent>
              </Sheet>
            </>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-teal-600 border-t border-teal-700 mt-auto">
        <div className="w-[98%] mx-auto py-0.5">
          <p className="text-center text-xs text-white">
            © {new Date().getFullYear()} IoT Solution s.r.l. - Sistema di Tracciamento GPS. Tutti i diritti riservati.
          </p>
        </div>
      </footer>
    </div>
  );
}

