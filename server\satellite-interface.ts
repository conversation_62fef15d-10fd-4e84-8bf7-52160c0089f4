import { InsertPosition, Position } from '@shared/schema';
import { storage } from './storage';
import { EventEmitter } from 'events';

/**
 * Questa classe gestisce l'interfaccia con il codice del satellitare.
 * Fornisce metodi per aggiornare i dati delle posizioni e notificare
 * le parti interessate degli aggiornamenti.
 */
class SatelliteInterface extends EventEmitter {
  private positions: Map<string, Position[]> = new Map();

  constructor() {
    super();
  }

  /**
   * Aggiorna i dati delle posizioni con un nuovo array di posizioni.
   * Questo metodo può essere chiamato dal tuo codice del satellitare
   * per aggiornare i dati.

  public async updatePositions(newPositions: InsertPosition[], satelliteId: string = "DEFAULT_DEVICE"): Promise<Position[]> {
    // Prima cancelliamo i dati precedenti per questo satellite
    await storage.clearPositions(satelliteId);

    // Poi aggiungiamo i nuovi dati con l'ID satellitare specificato
    const updatedPositions: Position[] = [];
    for (const position of newPositions) {
      const positionWithSatelliteId = { ...position, satelliteId };
      const newPosition = await storage.addPosition(positionWithSatelliteId);
      updatedPositions.push(newPosition);
    }

    // Aggiorniamo lo stato interno per questo satellite
    this.positions.set(satelliteId, updatedPositions);

    // Emettiamo un evento per notificare gli aggiornamenti, passando l'ID satellitare
    this.emit('positions-updated', updatedPositions, satelliteId);

    return updatedPositions;
  }

  /**
   * Aggiunge una singola posizione ai dati esistenti
   */
  public async addPosition(position: InsertPosition, satelliteId: string = "default", timestamp?: Date, deviceStatus: number = 0): Promise<Position> {
    // Assicuriamoci che la posizione abbia l'ID satellitare corretto
    const positionWithSatelliteId = { ...position, satelliteId };
    const newPosition = await storage.addPosition(positionWithSatelliteId, timestamp, deviceStatus);

    console.log(`Aggiunta nuova posizione dal modulo satellitare (${satelliteId}): ${position.latitude}, ${position.longitude}, Stato: ${deviceStatus}`);

    // Aggiorniamo lo stato interno per questo satellite
    const positions = await storage.getPositions(1000, satelliteId);
    this.positions.set(satelliteId, positions);

    // Emettiamo un evento per notificare gli aggiornamenti, passando l'ID satellitare
    this.emit('position-added', newPosition, satelliteId);

    // Aggiungiamo un breve ritardo prima di emettere l'evento positions-updated
    // per assicurarci che tutti i client ricevano l'aggiornamento
    setTimeout(() => {
      this.emit('positions-updated', positions, satelliteId);
    }, 100);

    return newPosition;
  }

  /**
   * Restituisce le posizioni correnti per un satellite specifico
   */
  public async getPositions(limit: number = 1000, satelliteId?: string): Promise<Position[]> {
    return storage.getPositions(limit, satelliteId);
  }

  /**
   * Aggiorna lo stato di connessione di un satellite
   * Questo metodo può essere chiamato dal codice del satellitare
   * per notificare lo stato di connessione
   */
  public updateConnectionStatus(connected: boolean, satelliteId?: string): void {
    const now = new Date().toISOString();
    console.log(`[${now}][satellite-interface.ts][updateConnectionStatus] Aggiornamento stato connessione satellitare: connected=${connected}, satelliteId=${satelliteId || 'sconosciuto'}`);

    // Emettiamo un evento per notificare gli aggiornamenti, passando l'ID satellitare
    this.emit('connection-status', connected, satelliteId);

    console.log(`[${now}][satellite-interface.ts][updateConnectionStatus] Emissione evento 'connection-status': connected=${connected}, satelliteId=${satelliteId || 'sconosciuto'}`);
  }

  /**
   * Imposta uno script di inizializzazione che verrà eseguito
   * all'avvio del server.
   *
   * Qui puoi importare e inizializzare il tuo codice del satellitare.
   */
  public initialize(callback: (satelliteInterface: SatelliteInterface) => void): void {
    // Chiamiamo la callback di inizializzazione passando questa istanza
    // in modo che il codice del satellitare possa interagire con essa
    callback(this);

    console.log('✅ Interfaccia satellitare inizializzata');
  }
}

// Esportiamo una singola istanza che sarà condivisa in tutta l'applicazione
export const satelliteInterface = new SatelliteInterface();