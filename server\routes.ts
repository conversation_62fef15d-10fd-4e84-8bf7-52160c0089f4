import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertPositionSchema, Position } from "@shared/schema";
import { fromZodError } from "zod-validation-error";
import { z } from "zod";
import { satelliteInterface } from "./satellite-interface";
import { initializeSatellite } from "./my-satellite-code";
import { WebSocketServer, WebSocket } from "ws";
import { generateKML } from "./export-utils";
import { parseCSV, parseKML } from "./import-utils-new";
import { setupAuth, hashPassword } from "./auth";
import { addSubscription, getVapidPublicKey, removeSubscription, sendNotificationBySatelliteId, getSubscriptionsCount } from "./push-notifications";
// Rimosso import setupCSVRoutes non utilizzato

export async function registerRoutes(app: Express): Promise<Server> {
  // Configura autenticazione
  setupAuth(app);

  // Rimosso setupCSVRoutes non utilizzato

  // Middleware per verificare che l'utente sia autenticato
  const ensureAuthenticated = (req: Request, res: Response, next: NextFunction) => {
    if (req.isAuthenticated()) {
      return next();
    }
    res.status(401).json({ message: "Accesso non autorizzato" });
  };

  // Middleware per verificare che l'utente sia un amministratore
  const ensureAdmin = (req: Request, res: Response, next: NextFunction) => {
    if (req.isAuthenticated() && req.user?.role === "admin") {
      return next();
    }
    res.status(403).json({ message: "Accesso riservato agli amministratori" });
  };

  // Get positions, filtered by user's satelliteId or a specific satelliteId if provided
  app.get("/api/positions", ensureAuthenticated, async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 1000;
      const userSatelliteIds = req.user?.satelliteId;

      // Gestione del parametro fromDate
      let fromDate: Date | undefined = undefined;
      if (req.query.fromDate) {
        try {
          fromDate = new Date(req.query.fromDate as string);
          // Verifica che la data sia valida
          if (isNaN(fromDate.getTime())) {
            return res.status(400).json({ message: "Formato data di inizio non valido" });
          }
        } catch (err) {
          return res.status(400).json({ message: "Formato data di inizio non valido" });
        }
      }

      // Gestione del parametro toDate
      let toDate: Date | undefined = undefined;
      if (req.query.toDate) {
        try {
          toDate = new Date(req.query.toDate as string);
          // Verifica che la data sia valida
          if (isNaN(toDate.getTime())) {
            return res.status(400).json({ message: "Formato data di fine non valido" });
          }
        } catch (err) {
          return res.status(400).json({ message: "Formato data di fine non valido" });
        }
      }

      // Verifica se l'utente ha un ID satellitare
      if (!userSatelliteIds) {
        return res.status(400).json({ message: "ID satellitare non associato all'utente" });
      }

      // Ottieni l'ID satellitare dalla query se presente, altrimenti usa quello dell'utente
      const requestedSatelliteId = req.query.satelliteId as string | undefined;

      // Se è stato richiesto un satellitare specifico, verifica che l'utente abbia accesso
      if (requestedSatelliteId) {
        // Dividi gli ID satellitari dell'utente
        const userSatelliteIdArray = userSatelliteIds.split(':');

        // Verifica che l'ID richiesto sia tra quelli dell'utente
        if (!userSatelliteIdArray.includes(requestedSatelliteId)) {
          return res.status(403).json({ message: "Non hai accesso a questo satellitare" });
        }

        // Usa l'ID satellitare richiesto
        let positions = await storage.getPositions(limit, requestedSatelliteId, fromDate, toDate);

        // Se non ci sono posizioni con la data selezionata e c'è una data di filtro,
        // prendi l'ultima posizione disponibile
        if (positions.length === 0 && fromDate) {
          const latestPositions = await storage.getPositions(1, requestedSatelliteId);
          if (latestPositions.length > 0) {
            positions = latestPositions;
            // Aggiungi un flag per indicare che questa è l'ultima posizione disponibile
            // anche se non rispetta il filtro per data
            positions[0].isLatestOnly = true;
          }
        }

        return res.json(positions);
      }

      // Se non è stato richiesto un satellitare specifico, usa il primo dell'utente
      const defaultSatelliteId = userSatelliteIds.split(':')[0];
      let positions = await storage.getPositions(limit, defaultSatelliteId, fromDate, toDate);

      // Se non ci sono posizioni con la data selezionata e c'è una data di filtro,
      // prendi l'ultima posizione disponibile
      if (positions.length === 0 && fromDate) {
        const latestPositions = await storage.getPositions(1, defaultSatelliteId);
        if (latestPositions.length > 0) {
          positions = latestPositions;
          // Aggiungi un flag per indicare che questa è l'ultima posizione disponibile
          // anche se non rispetta il filtro per data
          positions[0].isLatestOnly = true;
        }
      }

      res.json(positions);
    } catch (error) {
      console.error("Error fetching positions:", error);
      res.status(500).json({ message: "Errore nel recupero delle posizioni" });
    }
  });

  // Rimossa API POST /api/positions non utilizzata

  // Rimossa API POST /api/positions/import non utilizzata

  // Rimossa API DELETE /api/positions non utilizzata

  // Endpoint per esportare le posizioni in formato KML (Google Earth/Maps)
  app.get("/api/positions/export", ensureAuthenticated, async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 1000;
      const userSatelliteIds = req.user?.satelliteId;
      const format = req.query.format as string || 'kml';

      // Gestione del parametro satelliteId
      let requestedSatelliteId = req.query.satelliteId as string;

      // Se l'utente ha un ID satellitare con più dispositivi (separati da :)
      if (userSatelliteIds && userSatelliteIds.includes(':')) {
        const satelliteIdArray = userSatelliteIds.split(':');

        // Se non è stato specificato un ID o l'ID richiesto non è tra quelli dell'utente
        if (!requestedSatelliteId || !satelliteIdArray.includes(requestedSatelliteId)) {
          // Usa il primo ID come default
          requestedSatelliteId = satelliteIdArray[0];
        }
      } else {
        // Se l'utente ha un solo ID satellitare e non ne ha richiesto uno specifico
        if (!requestedSatelliteId) {
          requestedSatelliteId = userSatelliteIds || "default";
        }
      }

      // Gestione del parametro fromDate
      let fromDate: Date | undefined = undefined;
      if (req.query.fromDate) {
        try {
          fromDate = new Date(req.query.fromDate as string);
          // Verifica che la data sia valida
          if (isNaN(fromDate.getTime())) {
            return res.status(400).json({ message: "Formato data non valido" });
          }
        } catch (err) {
          return res.status(400).json({ message: "Formato data non valido" });
        }
      }

      // Gestione del parametro toDate
      let toDate: Date | undefined = undefined;
      if (req.query.toDate) {
        try {
          toDate = new Date(req.query.toDate as string);
          // Verifica che la data sia valida
          if (isNaN(toDate.getTime())) {
            return res.status(400).json({ message: "Formato data di fine non valido" });
          }
        } catch (err) {
          return res.status(400).json({ message: "Formato data di fine non valido" });
        }
      }

      // Ottieni le posizioni
      const positions = await storage.getPositions(limit, requestedSatelliteId, fromDate, toDate);

      if (positions.length === 0) {
        return res.status(404).json({ message: "Nessuna posizione trovata" });
      }

      // Esporta in base al formato richiesto
      if (format.toLowerCase() === 'kml') {
        // Genera il documento KML
        const kml = generateKML(positions, requestedSatelliteId);

        // Imposta gli header per il download del file
        res.setHeader('Content-Disposition', `attachment; filename="positions_${requestedSatelliteId}.kml"`);
        res.setHeader('Content-Type', 'application/vnd.google-earth.kml+xml');

        // Invia il file KML
        return res.send(kml);
      } else {
        return res.status(400).json({ message: "Formato non supportato" });
      }
    } catch (error) {
      console.error("Error exporting positions:", error);
      res.status(500).json({ message: "Errore nell'esportazione delle posizioni" });
    }
  });

  // Endpoint per ottenere la chiave pubblica VAPID
  app.get("/api/push/vapid-public-key", ensureAuthenticated, (req, res) => {
    res.send(getVapidPublicKey());
  });

  // Endpoint per registrare una sottoscrizione push
  app.post("/api/push/register", ensureAuthenticated, (req, res) => {
    try {
      const subscription = req.body;
      const satelliteId = req.user?.satelliteId;

      if (!subscription || !subscription.endpoint) {
        return res.status(400).json({ message: "Sottoscrizione non valida" });
      }

      // Convert null to undefined to match the expected type
      addSubscription(subscription, satelliteId || undefined);
      res.status(201).json({ message: "Sottoscrizione registrata con successo" });
    } catch (error) {
      console.error("Errore nella registrazione della sottoscrizione:", error);
      res.status(500).json({ message: "Errore nella registrazione della sottoscrizione" });
    }
  });

  // Endpoint per annullare una sottoscrizione push
  app.post("/api/push/unregister", ensureAuthenticated, (req, res) => {
    try {
      const { endpoint } = req.body;

      if (!endpoint) {
        return res.status(400).json({ message: "Endpoint non valido" });
      }

      const removed = removeSubscription(endpoint);

      if (removed) {
        res.status(200).json({ message: "Sottoscrizione rimossa con successo" });
      } else {
        res.status(404).json({ message: "Sottoscrizione non trovata" });
      }
    } catch (error) {
      console.error("Errore nella rimozione della sottoscrizione:", error);
      res.status(500).json({ message: "Errore nella rimozione della sottoscrizione" });
    }
  });

  // Endpoint per ottenere le sottoscrizioni push (solo per debug)
  app.get("/api/push/subscriptions", ensureAuthenticated, (req, res) => {
    try {
      const subscriptionsCount = getSubscriptionsCount();
      res.status(200).json({
        count: subscriptionsCount,
        message: `Ci sono ${subscriptionsCount} sottoscrizioni attive`
      });
    } catch (error) {
      console.error("Errore nel recupero delle sottoscrizioni:", error);
      res.status(500).json({ message: "Errore nel recupero delle sottoscrizioni" });
    }
  });

  // Endpoint per inviare una notifica push di test (solo per debug)
  app.post("/api/push/test", ensureAuthenticated, async (req, res) => {
    try {
      const { satelliteId } = req.body;

      if (!satelliteId) {
        return res.status(400).json({ message: "ID satellitare non specificato" });
      }

      console.log(`[DEBUG-PUSH-TEST] Invio notifica push di test per satelliteId: ${satelliteId}`);

      await sendNotificationBySatelliteId(
        satelliteId,
        'Test notifica push',
        'Questa è una notifica di test.'
      );

      res.status(200).json({ message: "Notifica di test inviata con successo" });
    } catch (error) {
      console.error("Errore nell'invio della notifica di test:", error);
      res.status(500).json({ message: "Errore nell'invio della notifica di test" });
    }
  });

  // Endpoint per verificare lo stato di connessione corrente
  app.get("/api/connection-status", ensureAuthenticated, (req, res) => {
    try {
      const satelliteId = req.query.satelliteId as string || req.user?.satelliteId;

      if (!satelliteId) {
        return res.status(400).json({ message: "ID satellitare non specificato" });
      }

      const isConnected = deviceConnectionStatus.get(satelliteId) || false;
      res.json({ connected: isConnected });
    } catch (error) {
      console.error("Errore nel recupero dello stato di connessione:", error);
      res.status(500).json({ message: "Errore nel recupero dello stato di connessione" });
    }
  });

  // Endpoint per aggiornare l'ID satellitare di un utente
  app.patch("/api/user/satellite-id", ensureAuthenticated, async (req, res) => {
    try {
      const { satelliteId } = req.body;

      if (!satelliteId || typeof satelliteId !== 'string') {
        return res.status(400).json({ message: "ID satellitare non valido" });
      }

      // Aggiorna l'ID satellitare dell'utente corrente
      const updatedUser = await storage.updateUserSatelliteId(req.user!.id, satelliteId);

      if (!updatedUser) {
        return res.status(404).json({ message: "Utente non trovato" });
      }

      // Aggiorna la sessione con il nuovo satelliteId
      req.login(updatedUser, (err) => {
        if (err) {
          return res.status(500).json({ message: "Errore durante l'aggiornamento della sessione" });
        }

        // Non inviare la password all'utente
        const { password, ...userWithoutPassword } = updatedUser;
        res.json(userWithoutPassword);
      });
    } catch (error) {
      console.error("Error updating satellite ID:", error);
      res.status(500).json({ message: "Errore durante l'aggiornamento dell'ID satellitare" });
    }
  });

  // ===== ENDPOINT AMMINISTRAZIONE =====

  // Ottieni tutti gli utenti (solo admin)
  app.get("/api/admin/users", ensureAdmin, async (req, res) => {
    try {
      console.log("[API] Richiesta di recupero utenti ricevuta");

      // Reinizializza gli utenti dal file CSV per assicurarsi che siano aggiornati
      storage.reinitializeUsers();

      const users = await storage.getAllUsers();
      console.log(`[API] Recuperati ${users.length} utenti dal database`);

      // Rimuovi le password prima di inviare i dati
      const usersWithoutPasswords = users.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      console.log(`[API] Inviando ${usersWithoutPasswords.length} utenti al frontend`);
      res.json(usersWithoutPasswords);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ message: "Errore nel recupero degli utenti" });
    }
  });

  // Ottieni tutti i clienti con satelliti (solo admin)
  app.get("/api/admin/clients", ensureAdmin, async (req, res) => {
    try {
      console.log("[API] Richiesta di recupero clienti ricevuta");

      // Reinizializza gli utenti dal file CSV per assicurarsi che siano aggiornati
      storage.reinitializeUsers();

      const users = await storage.getAllUsers();
      console.log(`[API] Recuperati ${users.length} utenti dal database`);

      // Filtra solo gli utenti che hanno satelliti e non sono admin
      const clients = users
        .filter(user => user.satelliteId && user.satelliteId.trim() !== "")
        .map(user => {
          const { password, ...clientWithoutPassword } = user;
          // Aggiungi un array di satelliti
          const satellites = user.satelliteId ? user.satelliteId.split(':') : [];
          return {
            ...clientWithoutPassword,
            satellites
          };
        });

      console.log(`[API] Inviando ${clients.length} clienti al frontend`);
      res.json(clients);
    } catch (error) {
      console.error("Error fetching clients:", error);
      res.status(500).json({ message: "Errore nel recupero dei clienti" });
    }
  });

  // Crea un nuovo utente (solo admin)
  app.post("/api/admin/users", ensureAdmin, async (req, res) => {
    try {
      const { username, password, name, satelliteId, role } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: "Username e password sono obbligatori" });
      }

      // Crea l'utente con password già hashata
      const hashedPassword = await hashPassword(password);
      const user = await storage.createUser({
        username,
        password: hashedPassword,
        name,
        satelliteId,
        role: role || "user"
      });

      // Rimuovi la password prima di inviare la risposta
      const { password: _, ...userWithoutPassword } = user;
      res.status(201).json(userWithoutPassword);
    } catch (error) {
      console.error("Error creating user:", error);
      res.status(500).json({ message: "Errore nella creazione dell'utente" });
    }
  });

  // Aggiorna un utente esistente (solo admin)
  app.put("/api/admin/users/:userId", ensureAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const { name, satelliteId, role } = req.body;

      // Aggiorna l'utente
      const updatedUser = await storage.updateUser(userId, {
        name,
        satelliteId,
        role
      });

      if (!updatedUser) {
        return res.status(404).json({ message: "Utente non trovato" });
      }

      // Rimuovi la password prima di inviare la risposta
      const { password, ...userWithoutPassword } = updatedUser;
      res.json(userWithoutPassword);
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ message: "Errore nell'aggiornamento dell'utente" });
    }
  });

  // Elimina un utente (solo admin)
  app.delete("/api/admin/users/:userId", ensureAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      // Verifica che l'utente non stia cercando di eliminare se stesso
      if (userId === req.user!.id) {
        return res.status(400).json({ message: "Non puoi eliminare il tuo account" });
      }

      const deleted = await storage.deleteUser(userId);

      if (!deleted) {
        return res.status(404).json({ message: "Utente non trovato" });
      }

      res.status(200).json({ message: "Utente eliminato con successo" });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: "Errore nell'eliminazione dell'utente" });
    }
  });

  // Esporta tutte le posizioni (solo admin)
  app.get("/api/admin/positions/export", ensureAdmin, async (req, res) => {
    try {
      const format = req.query.format as string || 'json';
      const satelliteId = req.query.satelliteId as string;
      const clientId = req.query.clientId as string;

      // Gestione del parametro fromDate
      let fromDate: Date | undefined = undefined;
      if (req.query.fromDate) {
        try {
          fromDate = new Date(req.query.fromDate as string);
          if (isNaN(fromDate.getTime())) {
            return res.status(400).json({ message: "Formato data di inizio non valido" });
          }
        } catch (err) {
          return res.status(400).json({ message: "Formato data di inizio non valido" });
        }
      }

      // Gestione del parametro toDate
      let toDate: Date | undefined = undefined;
      if (req.query.toDate) {
        try {
          toDate = new Date(req.query.toDate as string);
          if (isNaN(toDate.getTime())) {
            return res.status(400).json({ message: "Formato data di fine non valido" });
          }
        } catch (err) {
          return res.status(400).json({ message: "Formato data di fine non valido" });
        }
      }

      // Se è stato specificato un clientId, ottieni i satelliti associati a quel cliente
      let satelliteIds: string[] = [];
      if (clientId) {
        const user = await storage.getUser(parseInt(clientId));
        if (user && user.satelliteId) {
          // Se è stato specificato anche un satelliteId specifico, usa solo quello
          if (satelliteId) {
            const userSatellites = user.satelliteId.split(':');
            if (userSatellites.includes(satelliteId)) {
              satelliteIds = [satelliteId];
            } else {
              return res.status(400).json({ message: "Il satellite specificato non appartiene al cliente selezionato" });
            }
          } else {
            // Altrimenti usa tutti i satelliti del cliente
            satelliteIds = user.satelliteId.split(':');
          }
        } else {
          return res.status(404).json({ message: "Cliente non trovato" });
        }
      } else if (satelliteId) {
        // Se è stato specificato solo un satelliteId senza clientId
        satelliteIds = [satelliteId];
      }

      // Ottieni tutte le posizioni (senza limite)
      let positions: Position[] = [];

      if (satelliteIds.length > 0) {
        // Se abbiamo satelliti specifici, ottieni le posizioni per ciascuno e uniscile
        for (const satId of satelliteIds) {
          const satPositions = await storage.getPositions(100000, satId, fromDate, toDate);
          positions = [...positions, ...satPositions];
        }
      } else {
        // Se non abbiamo filtri di satellite o cliente, ottieni tutte le posizioni
        positions = await storage.getPositions(100000, undefined, fromDate, toDate);
      }

      if (positions.length === 0) {
        return res.status(404).json({ message: "Nessuna posizione trovata" });
      }

      // Prepara il nome del file
      let fileName = "positions";
      if (clientId) {
        // Se c'è un cliente, aggiungi il suo nome o ID al nome del file
        const client = await storage.getUser(parseInt(clientId));
        if (client) {
          fileName += `_client_${client.name || client.id}`;
        } else {
          fileName += `_client_${clientId}`;
        }

        // Se c'è anche un satellite specifico, aggiungilo
        if (satelliteId) {
          fileName += `_${satelliteId}`;
        } else if (satelliteIds.length > 0) {
          fileName += "_all_satellites";
        }
      } else if (satelliteId) {
        // Se c'è solo un satellite, usa quello
        fileName += `_${satelliteId}`;
      } else {
        // Altrimenti è "all"
        fileName += "_all";
      }

      // Esporta in base al formato richiesto
      if (format.toLowerCase() === 'json') {
        // Imposta gli header per il download del file
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}.json"`);
        res.setHeader('Content-Type', 'application/json');

        // Invia il file JSON
        return res.json(positions);
      } else if (format.toLowerCase() === 'csv') {
        // Converti le posizioni in CSV
        const csv = positions.map(pos => {
          return `${pos.id},${pos.latitude},${pos.longitude},${pos.speed || ''},${pos.satellites || ''},${pos.course || ''},${pos.batteryLevel || ''},${pos.timestamp.toISOString()},${pos.serverTimestamp.toISOString()},${pos.deviceStatus},${pos.satelliteId}`;
        }).join('\n');

        // Aggiungi l'intestazione
        const header = 'id,latitude,longitude,speed,satellites,course,batteryLevel,timestamp,serverTimestamp,deviceStatus,satelliteId\n';
        const csvContent = header + csv;

        // Imposta gli header per il download del file
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}.csv"`);
        res.setHeader('Content-Type', 'text/csv');

        // Invia il file CSV
        return res.send(csvContent);
      } else if (format.toLowerCase() === 'kml') {
        // Genera il documento KML
        // Se abbiamo più satelliti, usa un nome generico
        const kmlTitle = satelliteIds.length > 1 ? "Multiple Satellites" : (satelliteId || "All Satellites");
        const kml = generateKML(positions, kmlTitle);

        // Imposta gli header per il download del file
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}.kml"`);
        res.setHeader('Content-Type', 'application/vnd.google-earth.kml+xml');

        // Invia il file KML
        return res.send(kml);
      } else {
        return res.status(400).json({ message: "Formato non supportato" });
      }
    } catch (error) {
      console.error("Error exporting positions:", error);
      res.status(500).json({ message: "Errore nell'esportazione delle posizioni" });
    }
  });

  // Importa posizioni (solo admin)
  app.post("/api/admin/positions/import", ensureAdmin, async (req, res) => {
    try {
      const { format, content, positions } = req.body;

      console.log(`[IMPORT] Richiesta importazione formato: ${format}, contenuto presente: ${!!content}, positions presenti: ${!!positions}`);

      let positionsToImport: any[] = [];

      // Gestisci diversi formati di importazione
      if (format === 'json') {
        // Per JSON, verifica che positions sia un array valido
        if (!positions || !Array.isArray(positions) || positions.length === 0) {
          return res.status(400).json({ message: "Formato dati non valido" });
        }

        // Verifica che ogni posizione abbia i campi obbligatori e converti i timestamp in Date
        for (const position of positions) {
          // Verifica campi obbligatori
          if (typeof position.latitude !== 'number' || typeof position.longitude !== 'number') {
            return res.status(400).json({
              message: "Formato dati non valido: latitude e longitude devono essere numeri"
            });
          }

          // Assicurati che il satelliteId sia presente
          if (!position.satelliteId) {
            position.satelliteId = "default";
          }

          // Converti i timestamp in Date se sono stringhe
          if (position.timestamp && typeof position.timestamp === 'string') {
            try {
              position.timestamp = new Date(position.timestamp);
            } catch (error) {
              return res.status(400).json({
                message: `Formato timestamp non valido: ${position.timestamp}`
              });
            }
          }

          // Converti serverTimestamp in Date se è una stringa
          if (position.serverTimestamp && typeof position.serverTimestamp === 'string') {
            try {
              position.serverTimestamp = new Date(position.serverTimestamp);
            } catch (error) {
              return res.status(400).json({
                message: `Formato serverTimestamp non valido: ${position.serverTimestamp}`
              });
            }
          }
        }

        positionsToImport = positions;
        console.log(`[IMPORT] Validazione JSON completata: ${positionsToImport.length} posizioni valide`);
      }
      else if (format === 'csv') {
        // Per CSV, usa il parser CSV
        if (!content || typeof content !== 'string') {
          return res.status(400).json({ message: "Contenuto CSV mancante o non valido" });
        }

        try {
          positionsToImport = parseCSV(content);
          console.log(`[IMPORT] Parsing CSV completato: ${positionsToImport.length} posizioni trovate`);
        } catch (parseError) {
          console.error("[IMPORT] Errore parsing CSV:", parseError);
          return res.status(400).json({ message: `Errore nel parsing CSV: ${parseError instanceof Error ? parseError.message : String(parseError)}` });
        }
      }
      else if (format === 'kml') {
        // Per KML, usa il parser KML
        if (!content || typeof content !== 'string') {
          return res.status(400).json({ message: "Contenuto KML mancante o non valido" });
        }

        console.log(`[IMPORT] Contenuto KML ricevuto (primi 200 caratteri): ${content.substring(0, 200)}...`);

        try {
          positionsToImport = parseKML(content);
          console.log(`[IMPORT] Parsing KML completato: ${positionsToImport.length} posizioni trovate`);

          // Se non ci sono posizioni, ma il parsing è andato a buon fine, restituisci un messaggio più specifico
          if (positionsToImport.length === 0) {
            return res.status(400).json({
              message: "Nessuna posizione trovata nel file KML. Il file potrebbe non contenere punti validi o potrebbe avere un formato non supportato."
            });
          }
        } catch (parseError) {
          console.error("[IMPORT] Errore parsing KML:", parseError);
          return res.status(400).json({ message: `Errore nel parsing KML: ${parseError instanceof Error ? parseError.message : String(parseError)}` });
        }
      }
      else {
        return res.status(400).json({ message: "Formato non supportato" });
      }

      // Verifica che ci siano posizioni da importare
      if (positionsToImport.length === 0) {
        return res.status(400).json({ message: "Nessuna posizione valida trovata nel file" });
      }

      // Importa le posizioni
      console.log(`[IMPORT] Inizio importazione di ${positionsToImport.length} posizioni`);

      try {
        const importedPositions = await storage.importPositions(positionsToImport);

        console.log(`[IMPORT] Importazione completata con successo: ${importedPositions.length} posizioni importate`);

        // Log di debug per verificare che i timestamp siano oggetti Date
        if (importedPositions.length > 0) {
          const firstPosition = importedPositions[0];
          console.log(`[IMPORT-DEBUG] Prima posizione importata:
            id: ${firstPosition.id}
            timestamp: ${firstPosition.timestamp} (tipo: ${typeof firstPosition.timestamp})
            timestamp instanceof Date: ${firstPosition.timestamp instanceof Date}
            serverTimestamp: ${firstPosition.serverTimestamp} (tipo: ${typeof firstPosition.serverTimestamp})
            serverTimestamp instanceof Date: ${firstPosition.serverTimestamp instanceof Date}
          `);
        }

        res.status(201).json({
          message: `Importate ${importedPositions.length} posizioni con successo`,
          count: importedPositions.length
        });
      } catch (importError) {
        console.error("[IMPORT] Errore durante l'importazione:", importError);
        res.status(500).json({
          message: `Errore durante l'importazione delle posizioni: ${importError instanceof Error ? importError.message : String(importError)}`
        });
      }
    } catch (error) {
      console.error("Error importing positions:", error);
      res.status(500).json({ message: "Errore nell'importazione delle posizioni" });
    }
  });

  const httpServer = createServer(app);

  // Inizializza WebSocket server
  const wss = new WebSocketServer({
    server: httpServer,
    path: '/ws'
  });

  // Map per tenere traccia delle connessioni WebSocket e relativi parametri
  const clientConnections = new Map<WebSocket, {
    satelliteId?: string,
    limit?: number,
    fromDate?: Date,
    toDate?: Date
  }>();

  // Map per tenere traccia dello stato di connessione dei dispositivi satellitari
  const deviceConnectionStatus = new Map<string, boolean>();

  // Map per tenere traccia dello stato isAlive dei client WebSocket
  const clientAliveStatus = new Map<WebSocket, boolean>();

  // Funzione per verificare se un dispositivo è connesso
  const isDeviceConnected = (deviceId: string): boolean => {
    return deviceConnectionStatus.get(deviceId) || false;
  };

  // Funzione per inviare posizioni a un client specifico
  const sendPositionsToClient = async (
    client: WebSocket,
    satelliteId: string,
    limit: number = 1000,
    fromDate?: Date,
    toDate?: Date
  ) => {
    const now = new Date().toISOString();

    if (client.readyState !== WebSocket.OPEN) {
      console.log(`[${now}][routes.ts][sendPositionsToClient] Client non è in stato OPEN, uscita`);
      return;
    }

    try {
      console.log(`[${now}][routes.ts][sendPositionsToClient] Recupero posizioni per client \nsatelliteId=${satelliteId} \nfromDate=${fromDate?.toISOString() || 'undefined'} \ntoDate=${toDate?.toISOString() || 'undefined'}`);

      const positions = await storage.getPositions(limit, satelliteId, fromDate, toDate);

      console.log(`[${now}][routes.ts][sendPositionsToClient] Recuperate ${positions.length} posizioni dal database`);

      if (positions.length > 0) {
        const firstTimestamp = new Date(positions[0].timestamp).toISOString();
        const lastTimestamp = new Date(positions[positions.length - 1].timestamp).toISOString();
        console.log(`[${now}][routes.ts][sendPositionsToClient] Range date posizioni: da ${firstTimestamp} a ${lastTimestamp}`);
      }

      console.log(`[${now}][routes.ts][sendPositionsToClient] Invio ${positions.length} posizioni al client`);

      client.send(JSON.stringify({
        type: 'positions-update',
        data: positions
      }));

      console.log(`[${now}][routes.ts][sendPositionsToClient] Posizioni inviate con successo`);
    } catch (error) {
      console.error(`[${now}][routes.ts][sendPositionsToClient] Errore nell'invio delle posizioni:`, error);
      client.send(JSON.stringify({
        type: 'error',
        message: 'Errore nel recupero delle posizioni'
      }));
      console.log(`[${now}][routes.ts][sendPositionsToClient] Messaggio di errore inviato al client`);
    }
  };

  // Funzione per inviare aggiornamenti a tutti i client interessati a un satellite specifico
  const broadcastPositions = async (satelliteId?: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][routes.ts][broadcastPositions] Broadcast posizioni per satelliteId=${satelliteId || 'tutti'}`);

    let clientsUpdated = 0;
    let totalClients = 0;

    // Utilizziamo forEach invece di for...of per evitare problemi di compatibilità
    clientConnections.forEach(async (connection, client) => {
      totalClients++;

      if (client.readyState !== WebSocket.OPEN || !connection.satelliteId) {
        console.log(`[${now}][routes.ts][broadcastPositions] Client ignorato: readyState=${client.readyState}, satelliteId=${connection.satelliteId || 'undefined'}`);
        return;
      }

      // Invia solo ai client interessati al satellite specificato
      if (!satelliteId || connection.satelliteId === satelliteId) {
        console.log(`[${now}][routes.ts][broadcastPositions] Invio posizioni a client con satelliteId=${connection.satelliteId}`);
        await sendPositionsToClient(
          client,
          connection.satelliteId,
          connection.limit,
          connection.fromDate,
          connection.toDate
        );
        clientsUpdated++;
      } else {
        console.log(`[${now}][routes.ts][broadcastPositions] Client ignorato: satelliteId=${connection.satelliteId} non corrisponde a ${satelliteId}`);
      }
    });

    console.log(`[${now}][routes.ts][broadcastPositions] Aggiornamento inviato a ${clientsUpdated}/${totalClients} client`);
  };

  // Funzione per inviare lo stato di connessione ai client interessati
  const broadcastConnectionStatus = (connected: boolean, satelliteId?: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][routes.ts][broadcastConnectionStatus] Richiesta broadcast stato connessione`);

    if (!satelliteId) {
      console.log(`[${now}][routes.ts][broadcastConnectionStatus] Nessun satelliteId specificato, uscita`);
      return;
    }

    console.log(`[${now}][routes.ts][broadcastConnectionStatus] Broadcast stato connessione per satelliteId=${satelliteId}: ${connected ? 'Connesso' : 'Disconnesso'}`);

    // Aggiorna lo stato di connessione nella mappa
    deviceConnectionStatus.set(satelliteId, connected);
    console.log(`[${now}][routes.ts][broadcastConnectionStatus] Stato connessione aggiornato nella mappa`);

    // Invia lo stato a tutti i client interessati
    let clientsUpdated = 0;
    let totalClients = 0;

    clientConnections.forEach((connection, client) => {
      totalClients++;

      if (client.readyState === WebSocket.OPEN && connection.satelliteId === satelliteId) {
        console.log(`[${now}][routes.ts][broadcastConnectionStatus] Invio stato connessione a client con satelliteId=${connection.satelliteId}`);
        client.send(JSON.stringify({
          type: 'connection-status',
          connected: connected
        }));
        clientsUpdated++;
      } else if (client.readyState !== WebSocket.OPEN) {
        console.log(`[${now}][routes.ts][broadcastConnectionStatus] Client ignorato: readyState=${client.readyState}`);
      } else if (connection.satelliteId !== satelliteId) {
        console.log(`[${now}][routes.ts][broadcastConnectionStatus] Client ignorato: satelliteId=${connection.satelliteId} non corrisponde a ${satelliteId}`);
      }
    });

    console.log(`[${now}][routes.ts][broadcastConnectionStatus] Stato connessione inviato a ${clientsUpdated}/${totalClients} client`);
  };

  // Intervallo di ping in millisecondi (30 secondi)
  const PING_INTERVAL = 30000;

  // Imposta un timer per inviare ping periodici a tutti i client
  const pingInterval = setInterval(() => {
    wss.clients.forEach((client) => {
      // Verifica se il client è ancora vivo in base alla proprietà isAlive
      if (clientAliveStatus.get(client) === false) {
        console.warn(`[SERVER-WS] Client non ha risposto al ping, terminazione connessione`);
        clientAliveStatus.delete(client);
        clientConnections.delete(client);
        return client.terminate();
      }

      // Imposta isAlive a false, sarà reimpostato a true quando riceveremo un pong
      clientAliveStatus.set(client, false);

      // Invia un ping e attende un pong
      client.ping();
    });
  }, PING_INTERVAL);

  // Pulisci l'intervallo quando il server si chiude
  wss.on('close', () => {
    clearInterval(pingInterval);
  });

  // Gestisci la connessione WebSocket
  wss.on('connection', async (ws, request) => {
    const now = new Date().toISOString();
    const clientIp = request.socket.remoteAddress;
    console.log(`[${now}][routes.ts][ws-connection] Nuova connessione WebSocket da ${clientIp}`);

    // Inizializza lo stato isAlive per questo client
    clientAliveStatus.set(ws, true);
    console.log(`[${now}][routes.ts][ws-connection] Stato isAlive inizializzato a true`);

    // Gestisci i pong dal client
    ws.on('pong', () => {
      const pongTime = new Date().toISOString();
      clientAliveStatus.set(ws, true);
      console.log(`[${pongTime}][routes.ts][ws-pong] Ricevuto pong da client ${clientIp}, isAlive impostato a true`);
    });

    // Inizializza la connessione senza parametri
    clientConnections.set(ws, {});
    console.log(`[${now}][routes.ts][ws-connection] Connessione inizializzata senza parametri`);

    // Gestisci i messaggi in arrivo dal client
    ws.on('message', async (message) => {
      const msgTime = new Date().toISOString();
      console.log(`[${msgTime}][routes.ts][ws-message] Ricevuto messaggio da client ${clientIp}`);

      try {
        const data = JSON.parse(message.toString());
        console.log(`[${msgTime}][routes.ts][ws-message] Tipo messaggio: ${data.type}`);

        // Gestione dei diversi tipi di messaggi
        switch (data.type) {
          case 'request-positions': {
            console.log(`[${msgTime}][routes.ts][ws-message] Elaborazione richiesta posizioni`);

            // Aggiorna i parametri della connessione
            const connection = clientConnections.get(ws);
            if (connection) {
              // Aggiorna satelliteId
              if (data.satelliteId) {
                connection.satelliteId = data.satelliteId;
                console.log(`[${msgTime}][routes.ts][ws-message] SatelliteId aggiornato: ${connection.satelliteId}`);
              }

              // Aggiorna fromDate
              if (data.fromDate) {
                try {
                  connection.fromDate = new Date(data.fromDate);
                  if (isNaN(connection.fromDate.getTime())) {
                    console.log(`[${msgTime}][routes.ts][ws-message] fromDate non valido: ${data.fromDate}, impostato a undefined`);
                    connection.fromDate = undefined;
                  } else {
                    console.log(`[${msgTime}][routes.ts][ws-message] fromDate aggiornato: ${connection.fromDate.toISOString()}`);
                  }
                } catch (err) {
                  console.log(`[${msgTime}][routes.ts][ws-message] Errore parsing fromDate: ${err}, impostato a undefined`);
                  connection.fromDate = undefined;
                }
              } else {
                console.log(`[${msgTime}][routes.ts][ws-message] fromDate non specificato, impostato a undefined`);
                connection.fromDate = undefined;
              }

              // Aggiorna toDate
              if (data.toDate) {
                try {
                  connection.toDate = new Date(data.toDate);
                  if (isNaN(connection.toDate.getTime())) {
                    console.log(`[${msgTime}][routes.ts][ws-message] toDate non valido: ${data.toDate}, impostato a undefined`);
                    connection.toDate = undefined;
                  } else {
                    console.log(`[${msgTime}][routes.ts][ws-message] toDate aggiornato: ${connection.toDate.toISOString()}`);
                  }
                } catch (err) {
                  console.log(`[${msgTime}][routes.ts][ws-message] Errore parsing toDate: ${err}, impostato a undefined`);
                  connection.toDate = undefined;
                }
              } else {
                console.log(`[${msgTime}][routes.ts][ws-message] toDate non specificato, impostato a undefined`);
                connection.toDate = undefined;
              }

              // Aggiorna limit se specificato
              if (data.limit && typeof data.limit === 'number') {
                connection.limit = data.limit;
                console.log(`[${msgTime}][routes.ts][ws-message] Limit aggiornato: ${connection.limit}`);
              }

              // Invia le posizioni al client
              if (connection.satelliteId) {
                console.log(`[${msgTime}][routes.ts][ws-message] Invio posizioni per satelliteId=${connection.satelliteId}`);
                await sendPositionsToClient(
                  ws,
                  connection.satelliteId,
                  connection.limit,
                  connection.fromDate,
                  connection.toDate
                );

                // Invia anche lo stato di connessione del satellite
                const isConnected = isDeviceConnected(connection.satelliteId);
                console.log(`[${msgTime}][routes.ts][ws-message] Invio stato connessione: ${isConnected}`);
                ws.send(JSON.stringify({
                  type: 'connection-status',
                  connected: isConnected
                }));
              } else {
                console.log(`[${msgTime}][routes.ts][ws-message] Nessun satelliteId specificato, invio errore`);
                ws.send(JSON.stringify({
                  type: 'error',
                  message: 'Nessun satelliteId specificato'
                }));
              }
            } else {
              console.log(`[${msgTime}][routes.ts][ws-message] Connessione non trovata nella mappa clientConnections`);
            }
            break;
          }

          case 'get-connection-status': {
            const now = new Date().toISOString();
            console.log(`[${now}][routes.ts][ws-message] Ricevuta richiesta get-connection-status per satelliteId=${data.satelliteId || 'sconosciuto'}`);

            if (data.satelliteId) {
              const isConnected = isDeviceConnected(data.satelliteId);
              console.log(`[${now}][routes.ts][ws-message] Stato connessione per satelliteId=${data.satelliteId}: ${isConnected}`);

              ws.send(JSON.stringify({
                type: 'connection-status',
                connected: isConnected
              }));

              console.log(`[${now}][routes.ts][ws-message] Invio risposta connection-status: connected=${isConnected}`);
            } else {
              console.log(`[${now}][routes.ts][ws-message] Richiesta get-connection-status senza satelliteId, nessuna risposta inviata`);
            }
            break;
          }

          // Manteniamo retrocompatibilità con i vecchi messaggi
          case 'set-satellite-id': {
            if (data.satelliteId) {
              const connection = clientConnections.get(ws);
              if (connection) {
                connection.satelliteId = data.satelliteId;

                // Invia le posizioni per il nuovo satelliteId
                if (connection.satelliteId) {
                  await sendPositionsToClient(
                    ws,
                    connection.satelliteId,
                    connection.limit,
                    connection.fromDate,
                    connection.toDate
                  );
                }
              }
            }
            break;
          }

          case 'update-date-range': {
            const connection = clientConnections.get(ws);
            if (connection) {
              // Aggiorna le date
              if (data.fromDate) {
                try {
                  connection.fromDate = new Date(data.fromDate);
                } catch (err) {
                  connection.fromDate = undefined;
                }
              } else {
                connection.fromDate = undefined;
              }

              if (data.toDate) {
                try {
                  connection.toDate = new Date(data.toDate);
                } catch (err) {
                  connection.toDate = undefined;
                }
              } else {
                connection.toDate = undefined;
              }

              console.log(`[SERVER-WS] Date aggiornate: fromDate=${connection.fromDate?.toISOString() || 'undefined'}, toDate=${connection.toDate?.toISOString() || 'undefined'}`);

              // Invia le posizioni con le nuove date
              if (connection.satelliteId) {
                await sendPositionsToClient(
                  ws,
                  connection.satelliteId,
                  connection.limit,
                  connection.fromDate,
                  connection.toDate
                );
              }
            }
            break;
          }

          default : {
            console.log(`[${msgTime}][routes.ts][ws-message] Tipo messaggio non riconosciuto: ${data.type}`);
          }
        }
      } catch (error) {
        console.error('[SERVER-WS] Errore nel parsing del messaggio:', error);
      }
    });

    // Gestione chiusura WebSocket
    ws.on('close', () => {
      const closeTime = new Date().toISOString();
      console.log(`[${closeTime}][routes.ts][ws-close] Client disconnesso da ${clientIp}`);

      // Ottieni i dettagli della connessione prima di rimuoverla
      const connection = clientConnections.get(ws);
      if (connection) {
        console.log(`[${closeTime}][routes.ts][ws-close] Dettagli connessione chiusa:`, {
          satelliteId: connection.satelliteId,
          fromDate: connection.fromDate?.toISOString(),
          toDate: connection.toDate?.toISOString(),
          limit: connection.limit
        });
      }

      // Rimuovi il client dalle mappe
      clientAliveStatus.delete(ws);
      clientConnections.delete(ws);
      console.log(`[${closeTime}][routes.ts][ws-close] Client rimosso dalle mappe di connessione`);
    });

    // Gestione errori WebSocket
    ws.on('error', (error) => {
      const errorTime = new Date().toISOString();
      console.error(`[${errorTime}][routes.ts][ws-error] Errore WebSocket da ${clientIp}:`, error.message);

      // Dettagli aggiuntivi sulla connessione
      const connection = clientConnections.get(ws);
      if (connection) {
        console.error(`[${errorTime}][routes.ts][ws-error] Dettagli connessione con errore:`, {
          satelliteId: connection.satelliteId,
          limit: connection.limit,
          fromDate: connection.fromDate?.toISOString(),
          toDate: connection.toDate?.toISOString()
        });
      } else {
        console.error(`[${errorTime}][routes.ts][ws-error] Nessun dettaglio connessione disponibile`);
      }
    });
  });

  // Aggiungi listener per gli eventi dell'interfaccia satellitare
  satelliteInterface.on('positions-updated', async (_positions: Position[], satelliteId?: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][routes.ts][positions-updated] Ricevuto evento positions-updated per satelliteId=${satelliteId || 'sconosciuto'}`);

    // Invia aggiornamento a tutti i client interessati
    await broadcastPositions(satelliteId);
  });

  satelliteInterface.on('position-added', async (_position: Position, satelliteId?: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][routes.ts][position-added] Ricevuto evento position-added per satelliteId=${satelliteId || 'sconosciuto'}`);

    // Aggiungiamo un breve ritardo prima di inviare l'aggiornamento
    // per assicurarci che il database sia stato aggiornato
    console.log(`[${now}][routes.ts][position-added] Attesa 200ms prima di inviare l'aggiornamento`);
    setTimeout(async () => {
      const timeoutNow = new Date().toISOString();
      console.log(`[${timeoutNow}][routes.ts][position-added] Timeout scaduto, invio aggiornamento`);
      await broadcastPositions(satelliteId);
    }, 200);
  });

  // Listener per gli aggiornamenti dello stato di connessione
  satelliteInterface.on('connection-status', (connected: boolean, satelliteId?: string) => {
    const now = new Date().toISOString();
    console.log(`[${now}][routes.ts][connection-status] Ricevuto evento connection-status: connected=${connected}, satelliteId=${satelliteId || 'sconosciuto'}`);

    if (!satelliteId) {
      console.log(`[${now}][routes.ts][connection-status] Nessun satelliteId specificato, uscita`);
      return; // Non fare nulla se non c'è un satelliteId
    }

    // Aggiorna lo stato di connessione del dispositivo nella mappa
    deviceConnectionStatus.set(satelliteId, connected);
    console.log(`[${now}][routes.ts][connection-status] Stato connessione aggiornato nella mappa: ${satelliteId}=${connected}`);

    // Invia lo stato di connessione a tutti i client che stanno monitorando questo satelliteId
    let clientsUpdated = 0;
    let totalClients = 0;

    clientConnections.forEach((connection, client) => {
      totalClients++;

      if (client.readyState === WebSocket.OPEN && connection.satelliteId === satelliteId) {
        console.log(`[${now}][routes.ts][connection-status] Invio stato connessione a client con satelliteId=${connection.satelliteId}`);
        const message = JSON.stringify({
          type: 'connection-status',
          connected: connected
        });
        client.send(message);
        clientsUpdated++;
      }
    });

    console.log(`[${now}][routes.ts][connection-status] Stato connessione inviato a ${clientsUpdated}/${totalClients} client`);
  });

  // Inizializza il modulo del satellitare
  satelliteInterface.initialize(initializeSatellite);

  // In alternativa, per scopi di test, puoi avviare la simulazione
  // satelliteInterface.startSimulation(10000); // Ogni 10 secondi

  return httpServer;
}

// Importa la funzione updateConnectionStatus
import './connection-status';

// Importa il server TCP
import './Gestionesatelliti.js';


