import { useEffect, useRef, useState } from 'react';
import type { Position } from '@shared/schema';

// Estendi il tipo Position per includere l'indice originale e la lunghezza totale
interface PositionWithIndex extends Position {
  originalIndex?: number;
  totalLength?: number;
}

// Funzione per calcolare un hash semplice delle posizioni
// Questo ci permette di determinare se le posizioni sono effettivamente cambiate
const calculatePositionsHash = (positions: Position[]): string => {
  if (!positions || positions.length === 0) return '';

  // Prendiamo gli ID, le coordinate, lo stato e il timestamp per un confronto più accurato
  // Questo ci permette di rilevare cambiamenti nelle posizioni anche se gli ID sono gli stessi
  const positionData = positions.map(p => ({
    id: p.id,
    lat: p.latitude,
    lng: p.longitude,
    status: p.deviceStatus,
    timestamp: p.timestamp
  }));

  // Ordiniamo per ID per garantire un confronto coerente
  positionData.sort((a, b) => a.id - b.id);

  // Creiamo una stringa che rappresenta i dati
  return positionData.map(p => `${p.id}:${p.lat},${p.lng},${p.status},${p.timestamp}`).join('|');
};

// Funzione per identificare a quale viaggio appartiene una posizione
const identifyJourneyForPosition = (position: Position, allPositions: Position[]): number => {
  // Ordina tutte le posizioni cronologicamente
  const chronologicalPositions = [...allPositions].sort((a, b) =>
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  // Trova tutte le posizioni di inizio (deviceStatus = 3)
  const startPositions = chronologicalPositions.filter(pos => pos.deviceStatus === 3);

  // Se non ci sono posizioni di inizio, restituisci 1 (primo viaggio)
  if (startPositions.length === 0) return 1;

  // Per le posizioni di inizio, troviamo il loro indice nell'array delle posizioni di inizio
  if (position.deviceStatus === 3) {
    const index = startPositions.findIndex(pos => pos.id === position.id);
    if (index !== -1) {
      return index + 1; // 1-based
    }
  }

  // Timestamp della posizione da verificare
  const positionTime = new Date(position.timestamp).getTime();

  // Trova il viaggio a cui appartiene la posizione
  for (let i = 0; i < startPositions.length; i++) {
    const startTime = new Date(startPositions[i].timestamp).getTime();

    // Se è l'ultimo viaggio o la posizione è prima dell'inizio del prossimo viaggio
    const nextStartPosition = startPositions[i + 1];
    const nextStartTime = nextStartPosition ? new Date(nextStartPosition.timestamp).getTime() : Infinity;

    if (positionTime >= startTime && positionTime < nextStartTime) {
      // Restituisce l'indice del viaggio (1-based)
      return i + 1;
    }
  }

  // Se la posizione è precedente al primo viaggio, restituisci il primo viaggio
  if (positionTime < new Date(startPositions[0].timestamp).getTime()) {
    return 1;
  }

  // Se non appartiene a nessun viaggio specifico, restituisci l'ultimo viaggio
  return startPositions.length;
};


interface MapProps {
  positions: (Position | PositionWithIndex)[];
  selectedPosition: Position | null;
  onPositionSelect: (position: Position) => void;
  isPlaying?: boolean;
  playbackPositions?: (Position | PositionWithIndex)[];
  selectedJourneyId?: number | null;
}

export default function Map({ positions, selectedPosition, onPositionSelect, isPlaying = false, playbackPositions, selectedJourneyId = null }: MapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const markersRef = useRef<{[key: number]: any}>({});
  const polylineRefs = useRef<any[]>([]); // Array di riferimenti alle polyline invece di una singola
  const previousPositionsLengthRef = useRef<number>(0);
  const previousSelectedIdRef = useRef<number | null>(null);
  const previousJourneyIdRef = useRef<number | null>(null);
  const previousIsPlayingRef = useRef<boolean>(false);
  const previousPositionsHashRef = useRef<string>('');
  const resizeObserverRef = useRef<ResizeObserver | null>(null);



  // Effetto per inizializzare la mappa
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Map.tsx][useEffect-initMap] Inizializzazione mappa`);

    // Verifichiamo che Leaflet sia definito globalmente
    if (!window.L) {
      console.error(`[${now}][Map.tsx][useEffect-initMap] Leaflet non è stato caricato correttamente!`);
      return;
    }

    const L = window.L;

    // Se abbiamo già una mappa o non abbiamo il container, usciamo
    if (mapRef.current || !mapContainerRef.current) {
      console.log(`[${now}][Map.tsx][useEffect-initMap] Mappa già inizializzata o container non disponibile, uscita`);
      return;
    }

    console.log(`[${now}][Map.tsx][useEffect-initMap] Creazione nuova mappa Leaflet`);

    // Creiamo la mappa con opzioni per rimuovere i controlli
    mapRef.current = L.map(mapContainerRef.current, {
      zoomControl: false,      // Rimuove i controlli dello zoom
      attributionControl: false // Rimuove l'attribuzione
    }).setView([45.4642, 9.1900], 15);

    console.log(`[${now}][Map.tsx][useEffect-initMap] Aggiunta layer OpenStreetMap`);

    // Aggiungiamo il layer della mappa senza attribuzione
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '',  // Rimuove l'attribuzione
      maxZoom: 19       // Imposta lo zoom massimo
    }).addTo(mapRef.current);

    console.log(`[${now}][Map.tsx][useEffect-initMap] Mappa inizializzata con successo`);

    // Cleanup quando il componente viene smontato
    return () => {
      const cleanupNow = new Date().toISOString();
      console.log(`[${cleanupNow}][Map.tsx][useEffect-initMap-cleanup] Pulizia mappa`);

      // Rimuoviamo tutte le polyline
      polylineRefs.current.forEach(polyline => {
        if (polyline) polyline.remove();
      });
      polylineRefs.current = [];
      console.log(`[${cleanupNow}][Map.tsx][useEffect-initMap-cleanup] Polyline rimosse`);

      // Rimuoviamo la mappa
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
        console.log(`[${cleanupNow}][Map.tsx][useEffect-initMap-cleanup] Mappa rimossa`);
      }
    };
  }, []);

  // Effetto per aggiornare i marker quando cambiano le posizioni o la selezione
  useEffect(() => {
    const now = new Date().toISOString();
    console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Aggiornamento marker e tracce`);

    // Verifichiamo che Leaflet e la mappa siano pronti
    if (!window.L || !mapRef.current) {
      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Leaflet o mappa non pronti, uscita`);
      return;
    }

    // Se siamo in modalità riproduzione e non ci sono posizioni di riproduzione, non facciamo nulla
    if (isPlaying && (!playbackPositions || playbackPositions.length === 0)) {
      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Modalità riproduzione senza posizioni, uscita`);
      return;
    }

    // Se non siamo in modalità riproduzione e non ci sono posizioni, non facciamo nulla
    if (!isPlaying && !positions.length) {
      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Nessuna posizione disponibile, uscita`);
      return;
    }

    const L = window.L;
    const map = mapRef.current;

    // Determiniamo quali posizioni utilizzare
    const positionsToShow = isPlaying && playbackPositions ? playbackPositions : positions;

    // Calcola l'hash delle posizioni attuali
    const currentPositionsHash = calculatePositionsHash(positionsToShow);

    // Verifica se le posizioni sono effettivamente cambiate
    const isSelectionChanged = selectedPosition?.id !== previousSelectedIdRef.current;
    const isJourneyChanged = selectedJourneyId !== previousJourneyIdRef.current;
    const isPlayingChanged = isPlaying !== previousIsPlayingRef.current;
    const isPositionsChanged = currentPositionsHash !== previousPositionsHashRef.current;

    // Se nulla è cambiato, aggiorna solo lo stile del marker selezionato
    if (!isSelectionChanged && !isJourneyChanged && !isPlayingChanged && !isPositionsChanged) {
      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Nessun cambiamento rilevante, aggiornamento minimo`);

      // Aggiorna solo lo stile del marker selezionato se necessario
      if (selectedPosition && markersRef.current[selectedPosition.id]) {
        const marker = markersRef.current[selectedPosition.id];

        // Se è un circleMarker, aggiorna lo stile
        if (marker.setStyle) {
          marker.setStyle({
            radius: 8,
            fillColor: '#eab308',
            color: '#ffffff',
            weight: 2,
            opacity: 1,
            fillOpacity: 0.8
          });
        }

        // Centra la mappa sulla posizione selezionata se è cambiata
        if (isSelectionChanged) {
          const currentZoom = map.getZoom();
          map.setView([selectedPosition.latitude, selectedPosition.longitude], currentZoom);
        }
      }

      // Aggiorna i riferimenti per il prossimo render
      previousSelectedIdRef.current = selectedPosition?.id || null;
      previousJourneyIdRef.current = selectedJourneyId;
      previousIsPlayingRef.current = isPlaying;

      return;
    }

    // Aggiorna i riferimenti per il prossimo render
    previousSelectedIdRef.current = selectedPosition?.id || null;
    previousJourneyIdRef.current = selectedJourneyId;
    previousIsPlayingRef.current = isPlaying;
    previousPositionsHashRef.current = currentPositionsHash;

    console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Inizio aggiornamento mappa con ${positionsToShow.length} posizioni`);
    console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Cambiamenti: posizioni=${isPositionsChanged}, selezione=${isSelectionChanged}, viaggio=${isJourneyChanged}, modalità=${isPlayingChanged}`);

    // Ripuliamo i marker precedenti
    console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Pulizia marker precedenti: ${Object.keys(markersRef.current).length}`);
    Object.values(markersRef.current).forEach((marker: any) => {
      if (marker) marker.remove();
    });
    markersRef.current = {};

    // Rimuoviamo le polyline precedenti
    console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Pulizia polyline precedenti: ${polylineRefs.current.length}`);
    polylineRefs.current.forEach(polyline => {
      if (polyline) polyline.remove();
    });
    polylineRefs.current = [];

    // Utilizziamo la variabile positionsToShow già definita sopra
    console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Posizioni da mostrare: ${positionsToShow.length}, modalità: ${isPlaying ? 'playback' : 'normale'}`);

    // Se non ci sono posizioni da mostrare, usciamo
    if (positionsToShow.length === 0) {
      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Nessuna posizione da mostrare, uscita`);
      return;
    }

    // Salviamo tutte le posizioni in una variabile separata
    const allPositions = [...positionsToShow];

    // Ordiniamo le posizioni cronologicamente
    const chronologicalPositions = [...allPositions].sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // Troviamo tutte le posizioni di inizio (deviceStatus = 3)
    const startPositions = chronologicalPositions.filter(pos => pos.deviceStatus === 3);

    // Rimossi log di debug

    // Filtriamo le posizioni in base al viaggio selezionato
    if (!isPlaying && selectedJourneyId !== null) {
      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Filtro posizioni per viaggio selezionato: ${selectedJourneyId}`);

      // Troviamo la posizione di inizio corrispondente al viaggio selezionato
      // (il selectedJourneyId è 1-based, quindi dobbiamo sottrarre 1 per ottenere l'indice 0-based)
      const selectedStartPosition = startPositions.length >= selectedJourneyId ?
        startPositions[selectedJourneyId - 1] : null;

      console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Posizione inizio viaggio ${selectedJourneyId}: ${selectedStartPosition ? 'trovata' : 'non trovata'}`);

      if (selectedStartPosition) {
        const selectedStartTime = new Date(selectedStartPosition.timestamp).getTime();
        console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Timestamp inizio viaggio: ${new Date(selectedStartTime).toISOString()}`);

        // Troviamo la posizione di fine successiva (se esiste)
        const endPositions = chronologicalPositions.filter(pos =>
          pos.deviceStatus === 4 && new Date(pos.timestamp).getTime() > selectedStartTime
        );
        const nextEndPosition = endPositions.length > 0 ? endPositions[0] : null;
        const endTime = nextEndPosition ? new Date(nextEndPosition.timestamp).getTime() : Infinity;

        console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Posizione fine viaggio: ${nextEndPosition ? 'trovata' : 'non trovata'}`);
        if (nextEndPosition) {
          console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Timestamp fine viaggio: ${new Date(endTime).toISOString()}`);
        }

        // Filtriamo le posizioni che appartengono a questo viaggio SOLO per i marker
        // ma manteniamo tutte le posizioni per le polyline
        const selectedJourneyPositions = chronologicalPositions.filter(pos => {
          const posTime = new Date(pos.timestamp).getTime();
          return posTime >= selectedStartTime && posTime <= endTime;
        });

        console.log(`[${now}][Map.tsx][useEffect-updateMarkers] Posizioni filtrate per viaggio ${selectedJourneyId}: ${selectedJourneyPositions.length}`);

        // Salviamo le posizioni del viaggio selezionato in una variabile separata
        // che useremo solo per i marker
        window.selectedJourneyPositions = selectedJourneyPositions;
      }
    }

    // Array di colori da utilizzare ciclicamente per i diversi viaggi
    const trackColors = ['#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16', '#d97706', '#6366f1'];

    // Definiamo un'interfaccia per i viaggi
    interface Journey {
      positions: (Position | PositionWithIndex)[];
      color: string;
      journeyId: number;
    }

    // Approccio alternativo: creiamo un viaggio per ogni posizione di inizio
    // Questo garantisce che tutti i viaggi vengano creati, anche se mancano le posizioni di fine
    const journeys: Journey[] = [];

    // Troviamo tutte le posizioni di inizio (deviceStatus = 3)
    const journeyStartPositions = chronologicalPositions.filter(pos => pos.deviceStatus === 3);

    // Rimossi log di debug

    // Per ogni posizione di inizio, creiamo un viaggio
    journeyStartPositions.forEach((startPosition, index) => {
      const startTime = new Date(startPosition.timestamp).getTime();
      const journeyId = index + 1; // 1-based
      const color = trackColors[(journeyId - 1) % trackColors.length];

      // Troviamo la prossima posizione di inizio (se esiste)
      const nextStartPosition = journeyStartPositions[index + 1];
      const nextStartTime = nextStartPosition ? new Date(nextStartPosition.timestamp).getTime() : Infinity;

      // Troviamo la posizione di fine successiva (se esiste)
      const endPositions = chronologicalPositions.filter(pos =>
        pos.deviceStatus === 4 &&
        new Date(pos.timestamp).getTime() > startTime &&
        new Date(pos.timestamp).getTime() < nextStartTime
      );

      const endPosition = endPositions.length > 0 ? endPositions[0] : null;
      const endTime = endPosition ? new Date(endPosition.timestamp).getTime() : nextStartTime;

      // Filtriamo le posizioni che appartengono a questo viaggio
      const journeyPositions = chronologicalPositions.filter(pos => {
        const posTime = new Date(pos.timestamp).getTime();
        return posTime >= startTime && posTime < endTime;
      });

      // Aggiungiamo la posizione di inizio se non è già inclusa
      if (!journeyPositions.some(pos => pos.id === startPosition.id)) {
        journeyPositions.unshift(startPosition);
      }

      // Aggiungiamo la posizione di fine se esiste e non è già inclusa
      if (endPosition && !journeyPositions.some(pos => pos.id === endPosition.id)) {
        journeyPositions.push(endPosition);
      }

      // Rimossi log di debug

      // Aggiungiamo il viaggio all'array
      journeys.push({
        positions: journeyPositions,
        color: color,
        journeyId: journeyId // Aggiungiamo l'ID del viaggio (1-based)
      });
    });

    // Rimossi log di debug

    // Gestione speciale per il playback
    if (isPlaying && playbackPositions && playbackPositions.length > 0) {
      // Rimossi log di debug

      // Array di colori da utilizzare ciclicamente per i diversi viaggi
      const trackColors = ['#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16', '#d97706', '#6366f1'];

      // Ordina le posizioni di playback cronologicamente
      const chronologicalPlaybackPositions = [...playbackPositions].sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      // Trova tutte le posizioni di inizio (deviceStatus = 3) nel playback
      const playbackStartPositions = chronologicalPlaybackPositions.filter(pos => pos.deviceStatus === 3);
      // Rimossi log di debug

      // Se non ci sono posizioni di inizio, crea una singola polyline per tutte le posizioni
      if (playbackStartPositions.length === 0) {
        // Determina il viaggio della prima posizione di playback
        const journeyId = identifyJourneyForPosition(playbackPositions[0], positions);
        // Rimossi log di debug

        // Usa il colore corretto in base all'ID del viaggio
        const lineColor = trackColors[(journeyId - 1) % trackColors.length];

        // Crea la polyline con il colore corretto
        const playbackPoints = playbackPositions.map(pos => [pos.latitude, pos.longitude]);
        // Rimossi log di debug

        const polyline = L.polyline(playbackPoints, {
          color: lineColor,
          weight: 4,
          opacity: 0.8
        }).addTo(map);

        // Salva il riferimento alla polyline
        polylineRefs.current.push(polyline);
      }
      // Se ci sono posizioni di inizio, crea una polyline per ogni segmento di viaggio
      else {
        // Aggiungi la prima posizione come punto di inizio se non è già una posizione di inizio
        if (chronologicalPlaybackPositions[0].deviceStatus !== 3) {
          playbackStartPositions.unshift(chronologicalPlaybackPositions[0]);
        }

        // Per ogni posizione di inizio, crea un segmento fino alla prossima posizione di inizio o alla fine
        for (let i = 0; i < playbackStartPositions.length; i++) {
          const startPos = playbackStartPositions[i];
          const startTime = new Date(startPos.timestamp).getTime();

          // Determina il viaggio di questa posizione di inizio
          let journeyId: number;

          // Se è una vera posizione di inizio (deviceStatus = 3), trova il suo indice nell'array delle posizioni di inizio
          if (startPos.deviceStatus === 3) {
            // Ordina tutte le posizioni cronologicamente
            const allChronologicalPositions = [...positions].sort((a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
            );

            // Trova tutte le posizioni di inizio
            const allStartPositions = allChronologicalPositions.filter(pos => pos.deviceStatus === 3);

            // Trova l'indice di questa posizione di inizio
            const index = allStartPositions.findIndex(pos => pos.id === startPos.id);
            if (index !== -1) {
              journeyId = index + 1; // 1-based
            } else {
              // Se non trovata (strano), usa la funzione generica
              journeyId = identifyJourneyForPosition(startPos, positions);
            }
          } else {
            // Se non è una vera posizione di inizio, usa la funzione generica
            journeyId = identifyJourneyForPosition(startPos, positions);
          }

          // Rimossi log di debug

          // Trova la fine di questo segmento (prossima posizione di inizio o fine del playback)
          const nextStartPos = playbackStartPositions[i + 1];
          const endTime = nextStartPos ? new Date(nextStartPos.timestamp).getTime() : Infinity;

          // Filtra le posizioni che appartengono a questo segmento
          const segmentPositions = chronologicalPlaybackPositions.filter(pos => {
            const posTime = new Date(pos.timestamp).getTime();
            return posTime >= startTime && posTime < endTime;
          });

          // Se ci sono posizioni in questo segmento, crea una polyline
          if (segmentPositions.length > 0) {
            // Usa il colore corretto in base all'ID del viaggio
            const lineColor = trackColors[(journeyId - 1) % trackColors.length];

            // Crea la polyline con il colore corretto
            const segmentPoints = segmentPositions.map(pos => [pos.latitude, pos.longitude]);
            // Rimossi log di debug

            const polyline = L.polyline(segmentPoints, {
              color: lineColor,
              weight: 4,
              opacity: 0.8
            }).addTo(map);

            // Salva il riferimento alla polyline
            polylineRefs.current.push(polyline);
          }
        }
      }
    }
    // Gestione normale (non playback)
    else {
      // Creiamo una polyline per ogni viaggio
      journeys.forEach(journey => {
        if (journey.positions.length > 1) {
          // Ordiniamo le posizioni del viaggio cronologicamente
          const sortedPositions = [...journey.positions].sort((a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );

          const points = sortedPositions.map(pos => [pos.latitude, pos.longitude]);

          // Rimossi log di debug e variabili non utilizzate

          // Se è selezionato un viaggio specifico, mostriamo solo quel viaggio
          // e non disegniamo gli altri
          let lineColor = journey.color;
          let lineOpacity = 0.8;
          let lineWeight = 4;

          // Rimossi log di debug e variabili non utilizzate

          // Usiamo direttamente l'ID del viaggio (journey.journeyId) invece di cercare di calcolarlo
          // Questo garantisce coerenza con il selettore dei viaggi
          let journeyNumber = journey.journeyId;

          // Rimossi log di debug

          // Determina se questo è il viaggio selezionato
          const isSelectedJourney = selectedJourneyId !== null && journeyNumber === selectedJourneyId;

          // Rimossi log di debug

          // Se è selezionato un viaggio specifico e questo non è il viaggio selezionato,
          // non disegniamo la polyline
          if (selectedJourneyId !== null && !isSelectedJourney) {
            // Rimossi log di debug
            return; // Salta questo viaggio e passa al successivo
          }

          // Se questo è il viaggio selezionato, evidenziamolo
          if (isSelectedJourney) {
            // Viaggio selezionato: più evidente
            lineOpacity = 1.0;
            lineWeight = 6;

            // Forza il colore corretto in base all'ID del viaggio
            // Questo garantisce che il colore corrisponda a quello nel selettore
            const trackColors = ['#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16', '#d97706', '#6366f1'];
            lineColor = trackColors[(journeyNumber - 1) % trackColors.length];

            // Rimossi log di debug
          }

          const polyline = L.polyline(points, {
            color: lineColor,
            weight: lineWeight,
            opacity: lineOpacity
          }).addTo(map);

          // Rimossi log di debug

          // Salviamo il riferimento alla polyline
          polylineRefs.current.push(polyline);
        }
      });
    }

    // Non adattiamo più la mappa automaticamente quando ci sono più posizioni
    // Questo permette all'utente di mantenere il controllo sulla vista della mappa

    // Aggiungiamo i marker per ogni posizione
    positionsToShow.forEach((position, index) => {
      // Durante il playback, mostriamo sempre tutti i marker delle posizioni di playback
      if (isPlaying) {
        // Rimossi log di debug
      }
      // Se non siamo in playback e è selezionato un viaggio specifico, mostriamo solo i marker relativi a quel viaggio
      else if (selectedJourneyId !== null && window.selectedJourneyPositions) {
        // Rimossi log di debug

        // Verifichiamo se la posizione corrente appartiene al viaggio selezionato
        const belongsToSelectedJourney = window.selectedJourneyPositions.some(pos => pos.id === position.id);

        // Rimossi log di debug

        // Se la posizione non appartiene al viaggio selezionato e non è la posizione selezionata, non la mostriamo
        if (!belongsToSelectedJourney && !(selectedPosition && position.id === selectedPosition.id)) {
          return; // Salta questa posizione e passa alla successiva
        }
      }

      const isSelected = selectedPosition && position.id === selectedPosition.id;
      const isLatestPosition = index === 0; // La prima posizione nell'array è la più recente
      const isLatestOnly = position.isLatestOnly === true; // Indica se è l'ultima posizione disponibile (anche se non rispetta il filtro per data)
      const isStartPosition = position.deviceStatus === 3; // Posizione di inizio (stato = 3)
      const isEndPosition = position.deviceStatus === 4; // Posizione di fine (stato = 4)

      // Colori dei marker:
      // - Giallo (#eab308) per la posizione selezionata
      // - Teal (#14b8a6) per l'ultima posizione normale
      // - Arancione (#f59e0b) per l'ultima posizione disponibile (quando non ci sono posizioni per la data selezionata)
      // - Verde (#22c55e) per le posizioni di inizio
      // - Rosso (#ef4444) per le posizioni di fine
      // - Grigio (#6b7280) per le altre posizioni
      const fillColor = isSelected ? '#eab308' :
                        isLatestOnly ? '#f59e0b' :
                        isLatestPosition ? '#14b8a6' :
                        isStartPosition ? '#22c55e' :
                        isEndPosition ? '#ef4444' :
                        '#6b7280';

      // Creazione del marker per ogni posizione
      let marker;

      // Icone speciali per inizio e fine
      if (isStartPosition) {
        // Icona personalizzata per posizione di inizio (PlayCircle verde)
        const startIcon = L.divIcon({
          className: 'custom-div-icon',
          html: `<div style="
            display: flex;
            justify-content: center;
            align-items: center;
            width: 28px;
            height: 28px;
            background-color: white;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
          ">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#22c55e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polygon points="10 8 16 12 10 16 10 8"></polygon>
            </svg>
          </div>`,
          iconSize: [28, 28],
          iconAnchor: [14, 14]
        });

        // Creiamo il marker con l'icona personalizzata
        marker = L.marker([position.latitude, position.longitude], {
          icon: startIcon
        }).addTo(map);
      } else if (isEndPosition) {
        // Icona personalizzata per posizione di fine (StopCircle rosso)
        const endIcon = L.divIcon({
          className: 'custom-div-icon',
          html: `<div style="
            display: flex;
            justify-content: center;
            align-items: center;
            width: 28px;
            height: 28px;
            background-color: white;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
          ">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#ef4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <rect x="9" y="9" width="6" height="6"></rect>
            </svg>
          </div>`,
          iconSize: [28, 28],
          iconAnchor: [14, 14]
        });

        // Creiamo il marker con l'icona personalizzata
        marker = L.marker([position.latitude, position.longitude], {
          icon: endIcon
        }).addTo(map);
      } else if (isLatestPosition) {
        // Icona personalizzata per l'ultima posizione
        const iconColor = '#14b8a6';

        const lastPositionIcon = L.divIcon({
          className: 'custom-div-icon',
          html: `<div style="
            background-color: ${iconColor};
            border: 2px solid white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            position: relative;
          ">
            <div style="
              position: absolute;
              top: -4px;
              left: -4px;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              border: 2px solid ${iconColor};
              opacity: 0.5;
              background: transparent;
            "></div>
          </div>`,
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });

        // Creiamo il marker con l'icona personalizzata
        marker = L.marker([position.latitude, position.longitude], {
          icon: lastPositionIcon
        }).addTo(map);
      } else {
        // Per le altre posizioni, utilizziamo i marker circolari come prima
        marker = L.circleMarker([position.latitude, position.longitude], {
          radius: isSelected ? 8 : 6,
          fillColor,
          color: '#ffffff',
          weight: isSelected ? 2 : 1,
          opacity: 1,
          fillOpacity: 0.8
        }).addTo(map);
      }

      // Aggiungiamo l'evento click senza popup
      marker.on('click', () => {
        // Non fare nulla se la riproduzione è attiva
        if (!isPlaying) {
          onPositionSelect(position);
        }
      });

      // Salviamo il riferimento al marker
      markersRef.current[position.id] = marker;
    });

    // Gestione della centratura della mappa

    // Determiniamo se è il primo caricamento
    // Nota: isSelectionChanged è già definito sopra
    const isFirstLoad = previousPositionsLengthRef.current === 0 && positions.length > 0;

    // Aggiorniamo i riferimenti per il prossimo render
    previousPositionsLengthRef.current = positions.length;
    previousSelectedIdRef.current = selectedPosition?.id || null;

    // Caso 1: Se c'è una sola posizione e la mappa non è stata ancora inizializzata
    // Centriamo la mappa su quella posizione solo al primo caricamento
    if (positionsToShow.length === 1 && !mapRef.current.zoomInitialized && isFirstLoad) {
      map.setView([positionsToShow[0].latitude, positionsToShow[0].longitude], 15);
      mapRef.current.zoomInitialized = true;
    }
    // Caso 2: Se è stato selezionato un viaggio specifico, facciamo lo zoom per mostrare l'intero percorso
    else if (selectedJourneyId !== null && !isPlaying) {
      console.log(`[DEBUG-MAP-ZOOM] Adattamento della vista per il viaggio selezionato: ${selectedJourneyId}`);

      // Troviamo il viaggio selezionato
      const selectedJourney = journeys.find(journey => journey.journeyId === selectedJourneyId);

      if (selectedJourney && selectedJourney.positions.length > 0) {
        console.log(`[DEBUG-MAP-ZOOM] Viaggio trovato con ${selectedJourney.positions.length} posizioni`);

        // Creiamo un bounds vuoto
        const bounds = L.latLngBounds([]);

        // Aggiungiamo tutte le posizioni del viaggio al bounds
        selectedJourney.positions.forEach(pos => {
          bounds.extend([pos.latitude, pos.longitude]);
        });

        // Verifichiamo che il bounds sia valido (contiene almeno un punto)
        if (bounds.isValid()) {
          console.log(`[DEBUG-MAP-ZOOM] Bounds valido: ${bounds.toString()}`);

          // Adattiamo la vista della mappa per mostrare l'intero percorso
          // Aggiungiamo un padding per avere un po' di spazio intorno al percorso
          map.fitBounds(bounds, {
            padding: [50, 50], // Padding in pixel [top/bottom, left/right]
            maxZoom: 18,       // Limitiamo lo zoom massimo
            animate: true      // Animiamo la transizione
          });

          console.log(`[DEBUG-MAP-ZOOM] Vista adattata per mostrare l'intero percorso`);
        } else {
          console.log(`[DEBUG-MAP-ZOOM] Bounds non valido, impossibile adattare la vista`);
        }
      } else {
        console.log(`[DEBUG-MAP-ZOOM] Viaggio selezionato non trovato o senza posizioni`);
      }
    }
    // Caso 3: Se è cambiata la posizione selezionata, centriamo la mappa su quella posizione
    else if (selectedPosition && markersRef.current[selectedPosition.id] && isSelectionChanged) {
      // Mantieni lo zoom corrente ma centra sulla posizione selezionata
      const currentZoom = map.getZoom();
      map.setView([selectedPosition.latitude, selectedPosition.longitude], currentZoom);
    }
    // In tutti gli altri casi, non modifichiamo la vista della mappa
  }, [positions, selectedPosition, onPositionSelect, playbackPositions, isPlaying, selectedJourneyId]);

  // Effetto per centrare la mappa sulla posizione selezionata
  useEffect(() => {
    // Verifica se la posizione selezionata è cambiata
    if (!selectedPosition || !markersRef.current[selectedPosition.id]) return;

    // Verifica se è la stessa posizione di prima
    if (selectedPosition.id === previousSelectedIdRef.current) return;

    // Aggiorna il riferimento
    previousSelectedIdRef.current = selectedPosition.id;

    // Centriamo la mappa sulla posizione selezionata senza cambiare lo zoom
    if (mapRef.current) {
      const currentZoom = mapRef.current.getZoom();
      mapRef.current.setView([selectedPosition.latitude, selectedPosition.longitude], currentZoom, {
        animate: true,
        duration: 0.5 // Animazione più veloce per migliorare la reattività
      });
    }
  }, [selectedPosition, isPlaying]);

  // Effetto per gestire il ridimensionamento della mappa
  useEffect(() => {
    // Verifichiamo che la mappa sia inizializzata
    if (!mapRef.current || !mapContainerRef.current) return;

    // Variabile per tenere traccia del timeout
    let resizeTimeout: number | null = null;

    // Funzione per aggiornare la mappa quando cambia la dimensione del contenitore
    const handleResize = () => {
      // Cancelliamo il timeout precedente se esiste
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      // Creiamo un nuovo timeout per evitare troppe chiamate ravvicinate
      resizeTimeout = window.setTimeout(() => {
        if (mapRef.current) {
          console.log('[DEBUG-MAP-RESIZE] Rilevato ridimensionamento della mappa, chiamata a invalidateSize()');
          // Chiamiamo invalidateSize() per aggiornare la mappa
          mapRef.current.invalidateSize({
            animate: false,
            pan: false
          });
        }
        resizeTimeout = null;
      }, 100);
    };

    // Creiamo un ResizeObserver per monitorare i cambiamenti di dimensione del contenitore
    if (!resizeObserverRef.current) {
      resizeObserverRef.current = new ResizeObserver(handleResize);

      // Iniziamo a osservare il contenitore della mappa
      resizeObserverRef.current.observe(mapContainerRef.current);
    }

    // Funzione per gestire l'evento di resize dei pannelli
    const handlePanelResize = () => {
      // Cancelliamo il timeout precedente se esiste
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      // Creiamo un nuovo timeout
      resizeTimeout = window.setTimeout(() => {
        console.log('[DEBUG-MAP-RESIZE] Rilevato ridimensionamento dei pannelli, chiamata a invalidateSize()');
        if (mapRef.current) {
          mapRef.current.invalidateSize({
            animate: false,
            pan: false
          });
        }
        resizeTimeout = null;
      }, 100);
    };

    // Aggiungiamo un listener per l'evento di resize dei pannelli
    window.addEventListener('panel-resize', handlePanelResize);

    // Aggiungiamo anche un listener per l'evento di resize della finestra
    window.addEventListener('resize', handleResize);

    // Pulizia quando il componente viene smontato
    return () => {
      // Cancelliamo il timeout se esiste
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }
      window.removeEventListener('panel-resize', handlePanelResize);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div
      ref={mapContainerRef}
      className="map-container rounded-lg border border-gray-200"
      style={{ height: "100%", width: "100%", minHeight: "300px", maxHeight: "100vh" }}
    />
  );
}

// Aggiungiamo questa dichiarazione per TypeScript
declare global {
  interface Window {
    L: any;
    selectedJourneyPositions?: Position[];
  }
}
